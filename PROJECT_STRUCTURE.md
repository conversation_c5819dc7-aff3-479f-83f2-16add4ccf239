# Cấu trúc dự án OneCommerce Backend

Dự án được tổ chức theo mô hình module-based và domain-driven design, giúp dễ dàng mở rộng và quản lý khi ứng dụng phát triển.

## Cấu trúc thư mục

```
src/
├── common/                  # Các thành phần dùng chung
│   ├── decorators/         # Custom decorators
│   ├── filters/            # Exception filters
│   ├── guards/             # Guards
│   ├── interceptors/       # Interceptors
│   ├── pipes/              # Custom pipes
│   ├── dto/                # DTOs dùng chung
│   └── utils/              # Utility functions
│
├── config/                 # Cấu hình ứng dụng
│   ├── app.config.ts       # Cấu hình ứng dụng
│   ├── database.config.ts  # Cấu hình database
│   └── index.ts            # Export tất cả cấu hình
│
├── core/                   # Core modules và services
│   └── database/           # Database core
│       ├── interfaces/     # Database interfaces
│       ├── mongodb/        # MongoDB implementation
│       └── postgres/       # PostgreSQL implementation
│
├── modules/                # Các module chức năng
│   ├── auth/               # Authentication module
│   │   ├── controllers/    # Auth controllers
│   │   ├── dto/            # Auth DTOs
│   │   ├── interfaces/     # Auth interfaces
│   │   ├── services/       # Auth services
│   │   ├── strategies/     # Auth strategies
│   │   └── auth.module.ts  # Auth module
│   │
│   ├── users/              # Users module
│   │   ├── controllers/    # User controllers
│   │   ├── dto/            # User DTOs
│   │   ├── entities/       # User entities
│   │   ├── repositories/   # User repositories
│   │   ├── services/       # User services
│   │   └── users.module.ts # Users module
│   │
│   └── products/           # Products module
│       ├── controllers/    # Product controllers
│       ├── dto/            # Product DTOs
│       ├── entities/       # Product entities
│       ├── repositories/   # Product repositories
│       ├── services/       # Product services
│       └── products.module.ts # Products module
│
├── shared/                 # Shared modules và utilities
│   ├── constants/          # Constants
│   ├── interfaces/         # Shared interfaces
│   └── types/              # Type definitions
│
├── app.controller.ts       # App controller
├── app.module.ts           # App module
├── app.service.ts          # App service
└── main.ts                 # Entry point
```

## Kiến trúc Repository Pattern và Dependency Injection

Dự án sử dụng Repository Pattern và Dependency Injection để tạo ra một kiến trúc linh hoạt, dễ bảo trì và dễ chuyển đổi giữa các loại database.

### Repository Pattern

- **Interfaces**: Định nghĩa các phương thức mà repository phải triển khai
- **Implementations**: Triển khai cụ thể cho từng loại database (PostgreSQL, MongoDB)
- **Abstract Classes**: Cung cấp các phương thức cơ bản cho các repository cụ thể

### Dependency Injection

- **Providers**: Đăng ký các service và repository
- **Injection Tokens**: Sử dụng token để inject các implementation cụ thể
- **Module Exports**: Export các provider để sử dụng ở các module khác

## Chuyển đổi giữa PostgreSQL và MongoDB

Dự án được thiết kế để dễ dàng chuyển đổi giữa PostgreSQL và MongoDB:

1. **PostgreSQL (Mặc định)**:
   - Sử dụng TypeORM với PostgreSQL
   - Repository kế thừa từ `AbstractRepository`
   - Entity kế thừa từ `AbstractEntity`

2. **MongoDB**:
   - Sử dụng MongoDB native driver
   - Repository kế thừa từ `AbstractMongodbRepository`
   - Sử dụng cùng entity với PostgreSQL

### Cách chuyển đổi

Để chuyển từ PostgreSQL sang MongoDB, chỉ cần thay đổi provider trong module:

```typescript
// Từ PostgreSQL
{
  provide: USER_REPOSITORY,
  useClass: UserRepository,
}

// Sang MongoDB
{
  provide: USER_REPOSITORY,
  useClass: UserMongodbRepository,
}
```

## Các tính năng chính

1. **Authentication**: JWT-based authentication
2. **Validation**: Sử dụng class-validator và class-transformer
3. **Swagger Documentation**: API documentation với Swagger
4. **Error Handling**: Global exception filter
5. **Logging**: Structured logging
6. **Configuration**: Environment-based configuration
7. **Database Abstraction**: Repository pattern cho database abstraction

## Hướng dẫn mở rộng

Khi thêm một tính năng mới, hãy tuân theo các bước sau:

1. Tạo một module mới trong thư mục `src/modules/`
2. Tạo các thành phần cần thiết: entities, repositories, services, controllers, DTOs
3. Đăng ký module trong `app.module.ts`

Ví dụ: Thêm một module mới `orders`:

```
src/modules/orders/
├── controllers/
│   └── orders.controller.ts
├── dto/
│   ├── create-order.dto.ts
│   └── update-order.dto.ts
├── entities/
│   └── order.entity.ts
├── repositories/
│   ├── order.repository.interface.ts
│   ├── order.repository.ts
│   └── order.mongodb.repository.ts
├── services/
│   └── orders.service.ts
└── orders.module.ts
```
