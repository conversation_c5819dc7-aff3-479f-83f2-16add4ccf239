# Git
.git
.gitignore
.github

# Docker
Dockerfile
.dockerignore
docker-compose*

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# NestJS
dist
coverage

# IDE
.idea
.vscode
*.sublime-workspace
*.sublime-project

# OS
.DS_Store
Thumbs.db

# Environment
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Tests
/test
/coverage

# Documentation
/docs
README.md
CHANGELOG.md
LICENSE

# Misc
.editorconfig
.prettierrc
.eslintrc
jest.config.js
nodemon.json
