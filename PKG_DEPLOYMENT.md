# Hướng dẫn đóng gói và triển khai ứng dụng với pkg

Tài liệu này hướng dẫn cách đóng gói ứng dụng NestJS thành một file thực thi độc lập (executable) sử dụng công cụ `pkg` và triển khai nó trong môi trường production.

## Giới thiệu về pkg

`pkg` là một công cụ cho phép đóng gói ứng dụng Node.js thành một file thực thi độc lập, không cần cài đặt Node.js trên máy chủ. Điều này giúp:

- Triển khai ứng dụng dễ dàng hơn
- Giảm kích thước triển khai (không cần node_modules)
- B<PERSON>o vệ mã nguồn (mã đã được biên dịch)
- Tăng hiệu suất khởi động

## Chuẩn bị

### 1. <PERSON>ài đặt pkg

```bash
npm install -g pkg
```

### 2. Cấu hình package.json

File `package.json` đã được cấu hình với các thông tin cần thiết:

```json
{
  "bin": "dist/main.js",
  "pkg": {
    "assets": [
      "dist/**/*",
      ".env*",
      "node_modules/**/*"
    ],
    "targets": [
      "node18-linux-x64",
      "node18-macos-x64",
      "node18-win-x64"
    ]
  }
}
```

- `bin`: Đường dẫn đến file JavaScript chính của ứng dụng
- `assets`: Các file sẽ được đóng gói cùng với ứng dụng
- `targets`: Các nền tảng mục tiêu để tạo file thực thi

## Đóng gói ứng dụng

### Phương pháp 1: Sử dụng npm script

```bash
# Biên dịch TypeScript và tạo file thực thi
npm run build:pkg
```

Lệnh này sẽ:
1. Biên dịch mã TypeScript thành JavaScript
2. Tạo các file thực thi cho Linux, macOS và Windows trong thư mục `pkg-build`

### Phương pháp 2: Sử dụng Docker

```bash
# Build Docker image
docker build -t onecommerce-api-builder --target builder .

# Sao chép các file thực thi từ container
docker create --name temp-container onecommerce-api-builder
docker cp temp-container:/app/pkg-build ./pkg-build
docker rm temp-container
```

## Triển khai ứng dụng

### 1. Triển khai trực tiếp

Sau khi đóng gói, bạn có thể sao chép file thực thi và file `.env` lên máy chủ và chạy:

```bash
# Linux/macOS
chmod +x onecommerce-api-linux
./onecommerce-api-linux

# Windows
onecommerce-api-win.exe
```

### 2. Triển khai với Docker

```bash
# Build và chạy container
docker-compose up -d
```

Docker Compose sẽ sử dụng file thực thi đã được đóng gói thay vì chạy Node.js.

### 3. Triển khai với systemd (Linux)

Tạo file `/etc/systemd/system/onecommerce-api.service`:

```ini
[Unit]
Description=OneCommerce API
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/app
ExecStart=/path/to/app/onecommerce-api-linux
Restart=on-failure
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

Sau đó:

```bash
sudo systemctl enable onecommerce-api
sudo systemctl start onecommerce-api
```

## Lưu ý quan trọng

1. **Biến môi trường**: File thực thi vẫn cần file `.env` để cấu hình. Đảm bảo file này được sao chép cùng với file thực thi.

2. **Cơ sở dữ liệu**: Ứng dụng vẫn cần kết nối đến cơ sở dữ liệu PostgreSQL. Đảm bảo cơ sở dữ liệu đã được cài đặt và cấu hình đúng trong file `.env`.

3. **Quyền thực thi**: Trên Linux/macOS, đảm bảo file thực thi có quyền thực thi:
   ```bash
   chmod +x onecommerce-api-linux
   ```

4. **Tường lửa**: Đảm bảo cổng 3000 (hoặc cổng được cấu hình) đã được mở trên tường lửa.

## Xử lý sự cố

### File thực thi không chạy

Kiểm tra:
- Quyền thực thi (Linux/macOS)
- Thư viện hệ thống cần thiết đã được cài đặt
- File `.env` tồn tại và có quyền đọc

### Lỗi kết nối cơ sở dữ liệu

Kiểm tra:
- Cấu hình cơ sở dữ liệu trong file `.env`
- Cơ sở dữ liệu đang chạy và có thể truy cập từ máy chủ
- Người dùng cơ sở dữ liệu có quyền truy cập đúng

### Lỗi khác

Chạy file thực thi với biến môi trường DEBUG để xem thêm thông tin:

```bash
DEBUG=* ./onecommerce-api-linux
```
