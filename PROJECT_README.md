# OneCommerce Backend

Backend API cho dự án OneCommerce, được xây dựng với NestJS, TypeORM và PostgreSQL.

## Cấu trúc dự án

Dự án được tổ chức theo mô hình module-based và domain-driven design, giúp dễ dàng mở rộng và quản lý khi ứng dụng phát triển.

```
src/
├── common/                  # Các thành phần dùng chung
│   ├── decorators/         # Custom decorators
│   ├── filters/            # Exception filters
│   ├── guards/             # Guards
│   ├── interceptors/       # Interceptors
│   └── utils/              # Utility functions
│
├── config/                 # Cấu hình ứng dụng
│
├── core/                   # Core modules và services
│   └── database/           # Database core
│       └── base/           # Base classes and interfaces
│
├── modules/                # Các module chức năng
│   ├── auth/               # Authentication module
│   ├── users/              # Users module
│   └── products/           # Products module
│
├── shared/                 # Shared modules và utilities
│   ├── constants/          # Constants
│   └── interfaces/         # Shared interfaces
```

## Kiến trúc Repository Pattern

Dự án sử dụng Repository Pattern để tạo ra một lớp trừu tượng giữa tầng business logic và tầng data access. Điều này giúp dễ dàng chuyển đổi giữa các loại database (như PostgreSQL và MongoDB) mà không cần thay đổi business logic.

### Các thành phần chính:

1. **Base Entity**: Lớp cơ sở cho tất cả các entity, cung cấp các trường chung như id, createdAt, updatedAt.
2. **Repository Interface**: Interface định nghĩa các phương thức mà repository phải triển khai.
3. **Repository Implementation**: Triển khai cụ thể của repository cho từng loại database.
4. **Dependency Injection**: Sử dụng token để inject repository implementation vào service.

## Path Aliases

Dự án sử dụng path aliases để tránh các đường dẫn tương đối phức tạp và làm cho code dễ đọc hơn:

```typescript
// Thay vì
import { SomeService } from '../../../some/path/to/service';

// Sử dụng
import { SomeService } from '@modules/some/path/to/service';
```

### Các alias được định nghĩa:

- `@app/*`: Trỏ đến thư mục `src/*`
- `@common/*`: Trỏ đến thư mục `src/common/*`
- `@config/*`: Trỏ đến thư mục `src/config/*`
- `@core/*`: Trỏ đến thư mục `src/core/*`
- `@modules/*`: Trỏ đến thư mục `src/modules/*`
- `@shared/*`: Trỏ đến thư mục `src/shared/*`

## Cài đặt và chạy

### Yêu cầu

- Node.js (>= 18.x)
- PostgreSQL (>= 14.x)

### Cài đặt

1. Clone repository:

```bash
git clone <repository-url>
cd onecommerce-backend
```

2. Cài đặt dependencies:

```bash
npm install
```

3. Tạo file .env:

```
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=onecommerce
DATABASE_SYNCHRONIZE=true
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=1d
```

4. Chạy ứng dụng:

```bash
# Development
npm run start:dev

# Production
npm run build
npm run start:prod
```

## API Documentation

Swagger UI được tích hợp và có thể truy cập tại:

```
http://localhost:3000/api/v1/docs
```

## Chuyển đổi sang MongoDB

Dự án được thiết kế để dễ dàng chuyển đổi từ PostgreSQL sang MongoDB. Xem hướng dẫn chi tiết tại [MONGODB_MIGRATION.md](MONGODB_MIGRATION.md).

## Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Tính năng chính

- **Authentication**: JWT-based authentication
- **Users Management**: CRUD operations for users
- **Products Management**: CRUD operations for products
- **Validation**: Request validation using class-validator
- **Swagger Documentation**: API documentation with Swagger
- **Database Abstraction**: Repository pattern for easy database switching
