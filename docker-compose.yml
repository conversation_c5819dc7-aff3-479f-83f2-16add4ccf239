version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: linux # Use 'linux', 'macos', or 'windows' based on your target platform
    container_name: onecommerce-api
    restart: always
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_NAME=${DATABASE_NAME}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_ACCESS_TOKEN_EXPIRES_IN=${JWT_ACCESS_TOKEN_EXPIRES_IN}
      - JWT_REFRESH_TOKEN_EXPIRES_IN=${JWT_REFRESH_TOKEN_EXPIRES_IN}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - postgres
      - redis
    networks:
      - onecommerce-network

  postgres:
    image: postgres:14-alpine
    container_name: onecommerce-postgres
    restart: always
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=${DATABASE_USERNAME}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
      - POSTGRES_DB=${DATABASE_NAME}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - onecommerce-network

  redis:
    image: redis:alpine
    container_name: onecommerce-redis
    restart: always
    ports:
      - '6379:6379'
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - onecommerce-network

networks:
  onecommerce-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
