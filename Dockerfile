# Stage 1: Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies including development dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Install pkg globally
RUN npm install -g pkg

# Create executable packages for different platforms
RUN npm run build:pkg

# Stage 2: Production stage for Linux
FROM alpine:latest AS linux

# Install dependencies required for running Node.js binaries
RUN apk add --no-cache libstdc++ libgcc

# Set working directory
WORKDIR /app

# Copy the executable from builder stage
COPY --from=builder /app/pkg-build/onecommerce-api-linux /app/onecommerce-api

# Make the executable executable
RUN chmod +x /app/onecommerce-api

# Copy configuration files
COPY --from=builder /app/.env* ./

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
CMD ["/app/onecommerce-api"]

# Stage 3: Production stage for macOS
FROM alpine:latest AS macos

# Install dependencies required for running Node.js binaries
RUN apk add --no-cache libstdc++ libgcc

# Set working directory
WORKDIR /app

# Copy the executable from builder stage
COPY --from=builder /app/pkg-build/onecommerce-api-macos /app/onecommerce-api

# Make the executable executable
RUN chmod +x /app/onecommerce-api

# Copy configuration files
COPY --from=builder /app/.env* ./

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
CMD ["/app/onecommerce-api"]

# Stage 4: Production stage for Windows
FROM alpine:latest AS windows

# Install dependencies required for running Node.js binaries
RUN apk add --no-cache libstdc++ libgcc

# Set working directory
WORKDIR /app

# Copy the executable from builder stage
COPY --from=builder /app/pkg-build/onecommerce-api-win.exe /app/onecommerce-api.exe

# Copy configuration files
COPY --from=builder /app/.env* ./

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
CMD ["/app/onecommerce-api.exe"]

# Default to Linux
FROM linux
