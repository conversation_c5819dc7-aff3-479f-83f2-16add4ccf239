# Database Setup Guide

This project is designed to work with both PostgreSQL and MongoDB, using TypeORM for PostgreSQL and the native MongoDB driver for MongoDB. The architecture follows the repository pattern and dependency injection principles to make it easy to switch between database systems.

## Current Configuration

By default, the project is configured to use PostgreSQL with TypeORM. The database connection is set up in the `DatabaseModule` and the repositories extend the `AbstractRepository` class.

## Environment Variables

The database connection is configured using environment variables:

```
# PostgreSQL Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=onecommerce
DATABASE_SYNCHRONIZE=true

# MongoDB Configuration (when switching to MongoDB)
DATABASE_TYPE=mongodb
DATABASE_MONGODB_URI=mongodb://localhost:27017
DATABASE_MONGODB_DATABASE=onecommerce
```

## Switching to MongoDB

To switch from PostgreSQL to MongoDB, follow these steps:

1. Install the MongoDB driver:

```bash
npm install mongodb
```

2. Update the `.env` file to use MongoDB:

```
DATABASE_TYPE=mongodb
DATABASE_MONGODB_URI=mongodb://localhost:27017
DATABASE_MONGODB_DATABASE=onecommerce
```

3. Update the `src/users/users.module.ts` file to use the MongoDB repository:

```typescript
import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { MongodbModule } from '../database/mongodb/mongodb.module';
import { UserMongodbRepository } from './repositories/user.mongodb.repository';
import { USER_REPOSITORY } from './repositories/user.repository.interface';

@Module({
  imports: [MongodbModule],
  controllers: [UsersController],
  providers: [
    UsersService,
    {
      provide: USER_REPOSITORY,
      useClass: UserMongodbRepository,
    },
  ],
  exports: [UsersService],
})
export class UsersModule {}
```

4. Update the `src/app.module.ts` file to use the MongoDB module:

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MongodbModule } from './database/mongodb/mongodb.module';
import { UsersModule } from './users/users.module';
import databaseConfig from './config/database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig],
    }),
    MongodbModule,
    UsersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

## Project Structure

The project follows a modular structure with clear separation of concerns:

- `src/database/` - Contains database-related code
  - `abstract.entity.ts` - Base entity class for TypeORM
  - `abstract.repository.ts` - Base repository class for TypeORM
  - `database.module.ts` - TypeORM database module
  - `database.providers.ts` - TypeORM database providers
  - `interfaces/` - Repository and entity interfaces
  - `mongodb/` - MongoDB-specific code
    - `abstract.mongodb.repository.ts` - Base repository class for MongoDB
    - `mongodb.module.ts` - MongoDB module
    - `mongodb.providers.ts` - MongoDB providers

- `src/users/` - Example feature module
  - `entities/` - User entity
  - `repositories/` - User repositories (TypeORM and MongoDB)
  - `dto/` - Data transfer objects
  - `users.controller.ts` - User controller
  - `users.service.ts` - User service
  - `users.module.ts` - User module

## Adding New Features

When adding new features, follow these steps to maintain compatibility with both database systems:

1. Create an entity that extends `AbstractEntity`
2. Create a repository interface that extends `IBaseRepository`
3. Create a TypeORM repository that extends `AbstractRepository`
4. Create a MongoDB repository that extends `AbstractMongodbRepository`
5. Use dependency injection to inject the appropriate repository based on the configuration

## Testing

To test the application with PostgreSQL:

```bash
# Start PostgreSQL (using Docker)
docker run --name postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d postgres

# Run the application
npm run start:dev
```

To test the application with MongoDB:

```bash
# Start MongoDB (using Docker)
docker run --name mongodb -p 27017:27017 -d mongo

# Update .env to use MongoDB
# DATABASE_TYPE=mongodb

# Run the application
npm run start:dev
```
