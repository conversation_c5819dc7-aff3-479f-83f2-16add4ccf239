# Hướng dẫn chuyển đổi từ PostgreSQL sang MongoDB

Dự án này được thiết kế để dễ dàng chuyển đổi từ PostgreSQL sang MongoDB. Dưới đây là các bước cần thực hiện để chuyển đổi:

## 1. <PERSON><PERSON><PERSON> đặt thư viện MongoDB

```bash
npm install mongodb
```

## 2. Tạo MongoDB Repository

Tạo một lớp repository mới cho MongoDB, triển khai interface repository đã có. Ví dụ cho User:

```typescript
// src/modules/users/repositories/user.mongodb.repository.ts
import { Inject, Injectable } from '@nestjs/common';
import { Db, Collection, ObjectId } from 'mongodb';
import { IUserRepository } from './user.repository.interface';
import { User } from '../entities/user.entity';

@Injectable()
export class UserMongodbRepository implements IUserRepository {
  private readonly collection: Collection<User>;

  constructor(
    @Inject('MONGODB_CONNECTION')
    private readonly db: Db,
  ) {
    this.collection = this.db.collection<User>('users');
  }

  async create(data: Partial<User>): Promise<User> {
    const now = new Date();
    const entity = {
      ...data,
      createdAt: now,
      updatedAt: now,
    } as User;
    
    const result = await this.collection.insertOne(entity as any);
    return { ...entity, id: result.insertedId.toString() } as User;
  }

  async findOne(criteria: Partial<User>): Promise<User | null> {
    const result = await this.collection.findOne(this.convertIdToObjectId(criteria) as any);
    return result ? this.convertObjectIdToString(result) : null;
  }

  async findOneById(id: string): Promise<User | null> {
    const objectId = new ObjectId(id);
    const result = await this.collection.findOne({ _id: objectId } as any);
    return result ? this.convertObjectIdToString(result) : null;
  }

  async findAll(criteria?: Partial<User>): Promise<User[]> {
    const results = await this.collection
      .find(criteria ? this.convertIdToObjectId(criteria) as any : {})
      .toArray();
    
    return results.map(result => this.convertObjectIdToString(result));
  }

  async update(id: string, data: Partial<User>): Promise<User> {
    const objectId = new ObjectId(id);
    const entity = await this.findOneById(id);
    
    if (!entity) {
      throw new Error(`User with id ${id} not found`);
    }
    
    const updateData = {
      ...data,
      updatedAt: new Date(),
    };
    
    await this.collection.updateOne(
      { _id: objectId } as any,
      { $set: updateData },
    );
    
    return { ...entity, ...updateData } as User;
  }

  async remove(id: string): Promise<void> {
    const objectId = new ObjectId(id);
    const entity = await this.findOneById(id);
    
    if (!entity) {
      throw new Error(`User with id ${id} not found`);
    }
    
    await this.collection.deleteOne({ _id: objectId } as any);
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.findOne({ email } as Partial<User>);
  }

  private convertIdToObjectId(data: Partial<User>): Partial<User> {
    if (!data) return data;
    
    const result = { ...data };
    if (result.id) {
      (result as any)._id = new ObjectId(result.id);
      delete result.id;
    }
    
    return result;
  }

  private convertObjectIdToString(data: User): User {
    const result = { ...data };
    if ((result as any)._id) {
      result.id = (result as any)._id.toString();
      delete (result as any)._id;
    }
    
    return result;
  }
}
```

## 3. Tạo MongoDB Connection Provider

```typescript
// src/core/database/mongodb.providers.ts
import { ConfigService } from '@nestjs/config';
import { MongoClient } from 'mongodb';

export const MONGODB_CONNECTION = 'MONGODB_CONNECTION';

export const mongodbProviders = [
  {
    provide: MONGODB_CONNECTION,
    inject: [ConfigService],
    useFactory: async (configService: ConfigService) => {
      const uri = configService.get<string>('database.mongodb.uri', 'mongodb://localhost:27017');
      const client = new MongoClient(uri);
      await client.connect();
      return client.db(configService.get<string>('database.mongodb.database', 'onecommerce'));
    },
  },
];
```

## 4. Tạo MongoDB Module

```typescript
// src/core/database/mongodb.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { mongodbProviders } from './mongodb.providers';
import databaseConfig from '../../config/database.config';

@Module({
  imports: [
    ConfigModule.forFeature(databaseConfig),
  ],
  providers: [...mongodbProviders],
  exports: [...mongodbProviders],
})
export class MongodbModule {}
```

## 5. Cập nhật Module để sử dụng MongoDB Repository

Chỉ cần thay đổi provider trong module:

```typescript
// src/modules/users/users.module.ts
import { Module } from '@nestjs/common';
import { UsersService } from './services/users.service';
import { UsersController } from './controllers/users.controller';
import { MongodbModule } from '../../core/database/mongodb.module';
import { UserMongodbRepository } from './repositories/user.mongodb.repository';
import { USER_REPOSITORY } from './repositories/user.repository.interface';

@Module({
  imports: [MongodbModule],
  controllers: [UsersController],
  providers: [
    UsersService,
    {
      provide: USER_REPOSITORY,
      useClass: UserMongodbRepository, // Thay đổi từ UserRepository sang UserMongodbRepository
    },
  ],
  exports: [UsersService],
})
export class UsersModule {}
```

## 6. Cập nhật App Module

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MongodbModule } from './core/database/mongodb.module'; // Thay đổi từ DatabaseModule
import { UsersModule } from './modules/users/users.module';
import { ProductsModule } from './modules/products/products.module';
import { AuthModule } from './modules/auth/auth.module';
import configs from './config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: configs,
      envFilePath: '.env',
    }),
    MongodbModule, // Thay đổi từ DatabaseModule
    UsersModule,
    ProductsModule,
    AuthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

## 7. Cập nhật .env

```
DATABASE_TYPE=mongodb
DATABASE_MONGODB_URI=mongodb://localhost:27017
DATABASE_MONGODB_DATABASE=onecommerce
```

## Lưu ý

- Không cần thay đổi các entity, service, controller vì chúng đã được thiết kế để làm việc với interface repository
- Chỉ cần triển khai interface repository cho MongoDB và thay đổi provider trong module
- Cấu trúc dự án đã được thiết kế để dễ dàng chuyển đổi giữa các loại database
