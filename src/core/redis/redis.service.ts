import { Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class RedisService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async get<T>(key: string): Promise<T | undefined> {
    const value = await this.cacheManager.get<T>(key);
    return value === null ? undefined : value;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    await this.cacheManager.set(key, value, ttl);
  }

  async del(key: string): Promise<void> {
    await this.cacheManager.del(key);
  }

  // Reset method is not available in newer versions of cache-manager
  // This is a placeholder for compatibility
  async reset(): Promise<void> {
    console.warn('Cache reset not implemented');
  }

  /**
   * Add a token to the blacklist
   * @param token The token to blacklist
   * @param expiresIn Time in seconds until the token expires
   */
  async blacklistToken(token: string, expiresIn: number): Promise<void> {
    await this.set(`bl_${token}`, 'blacklisted', expiresIn);
  }

  /**
   * Check if a token is blacklisted
   * @param token The token to check
   * @returns True if the token is blacklisted, false otherwise
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    const result = await this.get<string>(`bl_${token}`);
    return result === 'blacklisted';
  }

  /**
   * Store a refresh token
   * @param userId The user ID
   * @param deviceId The device ID
   * @param token The refresh token
   * @param expiresIn Time in seconds until the token expires
   */
  async storeRefreshToken(
    userId: number,
    deviceId: string,
    token: string,
    expiresIn: number,
  ): Promise<void> {
    // Store token by device ID
    await this.set(`rt_${deviceId}`, token, expiresIn);

    // Store token in user's tokens hash
    await this.set(`rt_user_${userId}_${deviceId}`, token, expiresIn);

    // Store device ID in user's device list
    const userDevicesKey = `user_devices_${userId}`;
    const devices = (await this.get<string[]>(userDevicesKey)) || [];

    if (!devices.includes(deviceId)) {
      devices.push(deviceId);
      await this.set(userDevicesKey, devices);
    }
  }

  /**
   * Get a refresh token by device ID
   * @param deviceId The device ID
   * @returns The refresh token or undefined if not found
   */
  async getRefreshToken(deviceId: string): Promise<string | undefined> {
    return this.get<string>(`rt_${deviceId}`);
  }

  /**
   * Remove a refresh token
   * @param userId The user ID
   * @param deviceId The device ID
   */
  async removeRefreshToken(userId: number, deviceId: string): Promise<void> {
    // Remove token by device ID
    await this.del(`rt_${deviceId}`);

    // Remove token from user's tokens hash
    await this.del(`rt_user_${userId}_${deviceId}`);

    // Remove device from user's device list
    const userDevicesKey = `user_devices_${userId}`;
    const devices = (await this.get<string[]>(userDevicesKey)) || [];
    const updatedDevices = devices.filter((id) => id !== deviceId);

    if (updatedDevices.length > 0) {
      await this.set(userDevicesKey, updatedDevices);
    } else {
      await this.del(userDevicesKey);
    }
  }

  /**
   * Get all device IDs for a user
   * @param userId The user ID
   * @returns Array of device IDs
   */
  async getUserDevices(userId: number): Promise<string[]> {
    return (await this.get<string[]>(`user_devices_${userId}`)) || [];
  }

  /**
   * Remove all refresh tokens for a user except the current device
   * @param userId The user ID
   * @param currentDeviceId The current device ID to keep
   */
  async removeAllRefreshTokensExceptCurrent(
    userId: number,
    currentDeviceId: string,
  ): Promise<void> {
    const devices = await this.getUserDevices(userId);

    for (const deviceId of devices) {
      if (deviceId !== currentDeviceId) {
        // Remove token by device ID
        await this.del(`rt_${deviceId}`);

        // Remove token from user's tokens hash
        await this.del(`rt_user_${userId}_${deviceId}`);
      }
    }

    // Update user's device list to only include current device
    await this.set(`user_devices_${userId}`, [currentDeviceId]);
  }

  /**
   * Remove all refresh tokens for a user
   * @param userId The user ID
   */
  async removeAllRefreshTokens(userId: number): Promise<void> {
    const devices = await this.getUserDevices(userId);

    for (const deviceId of devices) {
      // Remove token by device ID
      await this.del(`rt_${deviceId}`);

      // Remove token from user's tokens hash
      await this.del(`rt_user_${userId}_${deviceId}`);
    }

    // Remove user's device list
    await this.del(`user_devices_${userId}`);
  }
}
