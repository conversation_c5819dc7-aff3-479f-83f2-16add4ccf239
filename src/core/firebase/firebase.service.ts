import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';

@Injectable()
export class FirebaseService {
  private readonly logger = new Logger(FirebaseService.name);
  private readonly app: admin.app.App;

  constructor(private readonly configService: ConfigService) {
    const projectId = this.configService.get<string>('firebase.projectId');
    const privateKey = this.configService.get<string>('firebase.privateKey');
    const clientEmail = this.configService.get<string>('firebase.clientEmail');

    if (!projectId || !privateKey || !clientEmail) {
      this.logger.warn(
        'Firebase configuration is missing. Firebase features will be disabled.',
      );
      return;
    }

    try {
      // Check if Firebase app is already initialized
      if (admin.apps.length === 0) {
        this.app = admin.initializeApp({
          credential: admin.credential.cert({
            projectId,
            privateKey,
            clientEmail,
          }),
          // databaseURL: this.configService.get<string>('firebase.databaseURL'),
        });
      } else {
        this.app = admin.app();
      }

      this.logger.log('Firebase Admin SDK initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Firebase Admin SDK', error);
      throw error;
    }
  }

  /**
   * Verify Firebase ID token
   * @param idToken Firebase ID token
   * @returns Decoded token with user information
   */
  async verifyIdToken(idToken: string): Promise<admin.auth.DecodedIdToken> {
    try {
      if (!this.app) {
        throw new UnauthorizedException('Firebase is not configured');
      }

      const decodedToken = await admin.auth(this.app).verifyIdToken(idToken);
      return decodedToken;
    } catch (error) {
      this.logger.error('Failed to verify Firebase ID token', error);
      throw new UnauthorizedException('Invalid Firebase token');
    }
  }

  /**
   * Get user information from Firebase
   * @param uid Firebase user UID
   * @returns Firebase user record
   */
  async getUser(uid: string): Promise<admin.auth.UserRecord> {
    try {
      if (!this.app) {
        throw new UnauthorizedException('Firebase is not configured');
      }

      const userRecord = await admin.auth(this.app).getUser(uid);
      return userRecord;
    } catch (error) {
      this.logger.error('Failed to get Firebase user', error);
      throw new UnauthorizedException('Failed to get user information');
    }
  }

  /**
   * Check if Firebase is properly configured
   * @returns boolean indicating if Firebase is configured
   */
  isConfigured(): boolean {
    return !!this.app;
  }
}
