/**
 * Base repository interface that defines common methods for all repositories
 * This interface is designed to be implemented by all repositories in the application
 * It provides CRUD operations and can be extended for specific needs
 *
 * The interface is database-agnostic, allowing for easy switching between different databases
 */
export interface IBaseRepository<T> {
  /**
   * Create a new entity
   * @param data Partial entity data
   * @returns Promise with the created entity
   */
  create(data: Partial<T>): Promise<T>;

  /**
   * Find one entity by criteria
   * @param criteria Criteria to find the entity
   * @returns Promise with the found entity or null
   */
  findOne(criteria: Partial<T>): Promise<T | null>;

  /**
   * Find one entity by id
   * @param id Entity id
   * @returns Promise with the found entity or null
   */
  findOneById(id: number): Promise<T | null>;

  /**
   * Find all entities matching criteria
   * @param criteria Optional criteria to filter entities
   * @returns Promise with array of entities
   */
  findAll(criteria?: Partial<T>): Promise<T[]>;

  /**
   * Update an entity
   * @param id Entity id
   * @param data Partial entity data to update
   * @returns Promise with the updated entity
   */
  update(id: number, data: Partial<T>): Promise<T>;

  /**
   * Remove an entity
   * @param id Entity id
   * @returns Promise void
   */
  remove(id: number): Promise<void>;
}
