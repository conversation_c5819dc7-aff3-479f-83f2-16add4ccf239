import { FindOptionsWhere, Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { IBaseRepository } from '@core/database/base/base.repository.interface';
import { BaseEntity } from '@core/database/base/base.entity';

/**
 * Base repository implementation for TypeORM
 * This class is designed to be extended by all TypeORM repositories in the application
 * It provides implementation for the IBaseRepository interface using TypeORM
 *
 * When switching to another database (e.g., MongoDB), you would create a new implementation
 * of the IBaseRepository interface for that database, but keep the same interface
 */
export abstract class BaseRepository<T extends BaseEntity>
  implements IBaseRepository<T>
{
  constructor(protected readonly repository: Repository<T>) {}

  async create(data: Partial<T>): Promise<T> {
    const entity = this.repository.create(data as any);
    return this.repository.save(entity as any);
  }

  async findOne(criteria: Partial<T>): Promise<T | null> {
    return this.repository.findOne({ where: criteria as FindOptionsWhere<T> });
  }

  async findOneById(id: number): Promise<T | null> {
    return this.repository.findOne({ where: { id } as any });
  }

  async findAll(criteria?: Partial<T>): Promise<T[]> {
    return this.repository.find({ where: criteria as FindOptionsWhere<T> });
  }

  async update(id: number, data: Partial<T>): Promise<T> {
    const entity = await this.findOneById(id);

    if (!entity) {
      throw new NotFoundException(`Entity with id ${id} not found`);
    }

    Object.assign(entity, data);
    return this.repository.save(entity as any);
  }

  async remove(id: number): Promise<void> {
    const entity = await this.findOneById(id);

    if (!entity) {
      throw new NotFoundException(`Entity with id ${id} not found`);
    }

    await this.repository.remove(entity as any);
  }
}
