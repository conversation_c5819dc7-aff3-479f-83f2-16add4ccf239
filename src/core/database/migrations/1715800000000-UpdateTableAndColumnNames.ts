import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableAndColumnNames1715800000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Rename columns in users table
    await queryRunner.query(`
      ALTER TABLE "users" RENAME COLUMN "isActive" TO "is_active";
    `);

    // Rename columns in products table
    await queryRunner.query(`
      ALTER TABLE "products" RENAME COLUMN "createdAt" TO "created_at";
      ALTER TABLE "products" RENAME COLUMN "updatedAt" TO "updated_at";
    `);

    // Rename columns in refresh_tokens table if it exists
    const refreshTokensTableExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'refresh_tokens'
      );
    `);

    if (refreshTokensTableExists[0].exists) {
      await queryRunner.query(`
        ALTER TABLE "refresh_tokens" RENAME COLUMN "userId" TO "user_id";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "deviceId" TO "device_id";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "ipAddress" TO "ip_address";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "userAgent" TO "user_agent";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "expiresAt" TO "expires_at";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "isRevoked" TO "is_revoked";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "createdAt" TO "created_at";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "updatedAt" TO "updated_at";
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rename columns in users table back to original names
    await queryRunner.query(`
      ALTER TABLE "users" RENAME COLUMN "is_active" TO "isActive";
    `);

    // Rename columns in products table back to original names
    await queryRunner.query(`
      ALTER TABLE "products" RENAME COLUMN "created_at" TO "createdAt";
      ALTER TABLE "products" RENAME COLUMN "updated_at" TO "updatedAt";
    `);

    // Rename columns in refresh_tokens table back to original names if it exists
    const refreshTokensTableExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'refresh_tokens'
      );
    `);

    if (refreshTokensTableExists[0].exists) {
      await queryRunner.query(`
        ALTER TABLE "refresh_tokens" RENAME COLUMN "user_id" TO "userId";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "device_id" TO "deviceId";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "ip_address" TO "ipAddress";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "user_agent" TO "userAgent";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "expires_at" TO "expiresAt";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "is_revoked" TO "isRevoked";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "created_at" TO "createdAt";
        ALTER TABLE "refresh_tokens" RENAME COLUMN "updated_at" TO "updatedAt";
      `);
    }
  }
}
