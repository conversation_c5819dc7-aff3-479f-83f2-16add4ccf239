import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeIdToAutoIncrement1715700000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys first to avoid constraints issues
    await queryRunner.query(`
      DO $$
      DECLARE
        r RECORD;
      BEGIN
        FOR r IN (SELECT conname, conrelid::regclass AS table_name FROM pg_constraint WHERE contype = 'f')
        LOOP
          EXECUTE 'ALTER TABLE ' || r.table_name || ' DROP CONSTRAINT ' || r.conname;
        END LOOP;
      END $$;
    `);

    // Get all tables
    const tables = await queryRunner.query(`
      SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    `);

    // For each table, change the id column from UUID to SERIAL
    for (const table of tables) {
      const tableName = table.tablename;
      
      // Check if the table has an id column
      const hasIdColumn = await queryRunner.query(`
        SELECT column_name FROM information_schema.columns 
        WHERE table_name = '${tableName}' AND column_name = 'id'
      `);
      
      if (hasIdColumn.length > 0) {
        // Create a new temporary id column
        await queryRunner.query(`
          ALTER TABLE "${tableName}" ADD COLUMN temp_id SERIAL
        `);
        
        // Update the primary key constraint
        await queryRunner.query(`
          ALTER TABLE "${tableName}" DROP CONSTRAINT "${tableName}_pkey"
        `);
        
        // Drop the old id column
        await queryRunner.query(`
          ALTER TABLE "${tableName}" DROP COLUMN id
        `);
        
        // Rename the temporary id column to id
        await queryRunner.query(`
          ALTER TABLE "${tableName}" RENAME COLUMN temp_id TO id
        `);
        
        // Add the primary key constraint back
        await queryRunner.query(`
          ALTER TABLE "${tableName}" ADD CONSTRAINT "${tableName}_pkey" PRIMARY KEY (id)
        `);
      }
    }

    // Recreate foreign keys
    // Users to refresh_tokens
    await queryRunner.query(`
      ALTER TABLE "refresh_tokens" ADD CONSTRAINT "FK_refresh_tokens_users" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys first to avoid constraints issues
    await queryRunner.query(`
      DO $$
      DECLARE
        r RECORD;
      BEGIN
        FOR r IN (SELECT conname, conrelid::regclass AS table_name FROM pg_constraint WHERE contype = 'f')
        LOOP
          EXECUTE 'ALTER TABLE ' || r.table_name || ' DROP CONSTRAINT ' || r.conname;
        END LOOP;
      END $$;
    `);

    // Get all tables
    const tables = await queryRunner.query(`
      SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    `);

    // For each table, change the id column from SERIAL to UUID
    for (const table of tables) {
      const tableName = table.tablename;
      
      // Check if the table has an id column
      const hasIdColumn = await queryRunner.query(`
        SELECT column_name FROM information_schema.columns 
        WHERE table_name = '${tableName}' AND column_name = 'id'
      `);
      
      if (hasIdColumn.length > 0) {
        // Create a new temporary id column
        await queryRunner.query(`
          ALTER TABLE "${tableName}" ADD COLUMN temp_id UUID DEFAULT uuid_generate_v4()
        `);
        
        // Update the primary key constraint
        await queryRunner.query(`
          ALTER TABLE "${tableName}" DROP CONSTRAINT "${tableName}_pkey"
        `);
        
        // Drop the old id column
        await queryRunner.query(`
          ALTER TABLE "${tableName}" DROP COLUMN id
        `);
        
        // Rename the temporary id column to id
        await queryRunner.query(`
          ALTER TABLE "${tableName}" RENAME COLUMN temp_id TO id
        `);
        
        // Add the primary key constraint back
        await queryRunner.query(`
          ALTER TABLE "${tableName}" ADD CONSTRAINT "${tableName}_pkey" PRIMARY KEY (id)
        `);
      }
    }

    // Recreate foreign keys
    // Users to refresh_tokens
    await queryRunner.query(`
      ALTER TABLE "refresh_tokens" ADD CONSTRAINT "FK_refresh_tokens_users" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);
  }
}
