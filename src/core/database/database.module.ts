import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { databaseProviders } from './database.providers';
import databaseConfig from '@config/database.config';

/**
 * Database module that provides database connection
 * This module is imported by other modules that need database access
 */
@Module({
  imports: [ConfigModule.forFeature(databaseConfig)],
  providers: [...databaseProviders],
  exports: [...databaseProviders],
})
export class DatabaseModule {}
