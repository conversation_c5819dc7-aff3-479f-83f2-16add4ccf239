import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

/**
 * Database connection token for dependency injection
 */
export const DATABASE_CONNECTION = 'DATABASE_CONNECTION';

/**
 * Database providers for TypeORM
 * These providers are used to create and inject the database connection
 */
export const databaseProviders = [
  {
    provide: DATABASE_CONNECTION,
    inject: [ConfigService],
    useFactory: async (configService: ConfigService) => {
      const dataSource = new DataSource({
        type: configService.get<string>('database.type', 'postgres') as any,
        host: configService.get<string>('database.host', 'localhost'),
        port: configService.get<number>('database.port', 5432),
        username: configService.get<string>('database.username', 'postgres'),
        password: configService.get<string>('database.password', 'postgres'),
        database: configService.get<string>('database.database', 'onecommerce'),
        entities: configService.get<string[]>('database.entities', [
          __dirname + '/../../../**/*.entity{.ts,.js}',
        ]),
        synchronize: configService.get<boolean>('database.synchronize', false),
        namingStrategy: new SnakeNamingStrategy(),
      });

      return dataSource.initialize();
    },
  },
];
