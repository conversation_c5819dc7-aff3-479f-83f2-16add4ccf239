import { ApiProperty } from '@nestjs/swagger';
import { PaginationDto } from './pagination.dto';

export interface PageMetaDtoParameters {
  paginationDto: PaginationDto;
  totalItems: number;
}

export class PageMetaDto {
  @ApiProperty({
    description: 'Current page number (1-based)',
    type: Number,
    example: 1,
  })
  readonly page: number;

  @ApiProperty({
    description: 'Number of items per page',
    type: Number,
    example: 10,
  })
  readonly limit: number;

  @ApiProperty({
    description: 'Total number of items',
    type: Number,
    example: 100,
  })
  readonly totalItems: number;

  @ApiProperty({
    description: 'Total number of pages',
    type: Number,
    example: 10,
  })
  readonly totalPages: number;

  @ApiProperty({
    description: 'Whether there is a previous page',
    type: Boolean,
    example: false,
  })
  readonly hasPreviousPage: boolean;

  @ApiProperty({
    description: 'Whether there is a next page',
    type: Boolean,
    example: true,
  })
  readonly hasNextPage: boolean;

  constructor({ paginationDto, totalItems }: PageMetaDtoParameters) {
    this.page = paginationDto.page || 1;
    this.limit = paginationDto.limit || 10;
    this.totalItems = totalItems;
    this.totalPages = Math.ceil(this.totalItems / this.limit);
    this.hasPreviousPage = this.page > 1;
    this.hasNextPage = this.page < this.totalPages;
  }
}
