import { ApiProperty } from '@nestjs/swagger';
import { PageMetaDto } from './page-meta.dto';

export class ApiResponseDto<T> {
  @ApiProperty({
    description: 'Array of items',
    isArray: true,
  })
  readonly items: T[];

  @ApiProperty({
    description: 'Page metadata',
    type: PageMetaDto,
  })
  readonly meta: PageMetaDto;

  @ApiProperty({
    description: 'Response message',
    example: 'Success',
  })
  readonly message: string;

  constructor(items: T[], meta: PageMetaDto, message: string = 'Success') {
    this.items = items;
    this.meta = meta;
    this.message = message;
  }
}
