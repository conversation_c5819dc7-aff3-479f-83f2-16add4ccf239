import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { PaginationDto } from './pagination.dto';
import { QueryBuilderHelper } from '@common/database/query-builder.helper';
import { SelectQueryBuilder } from 'typeorm';
import { FilterBuilder, IFilter } from '@common/database/filters';

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * FilterOptionsDto class
 * Base class for all filter DTOs
 * Provides common properties and methods for filtering, sorting and pagination
 */
export class FilterOptionsDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Sort order',
    enum: SortOrder,
    default: SortOrder.ASC,
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.ASC;

  /**
   * Create a QueryBuilderHelper instance for the given entity
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   * @returns QueryBuilderHelper instance
   */
  createQueryBuilder<T extends object>(
    queryBuilder: SelectQueryBuilder<T>,
    alias: string = 'entity',
  ): QueryBuilderHelper<T> {
    return new QueryBuilderHelper<T>(queryBuilder, alias);
  }

  /**
   * Build filters for the query
   * This method should be overridden by subclasses to add specific filters
   * @returns Filter to apply
   */
  buildFilters(): IFilter | null {
    // To be implemented by subclasses
    return null;
  }

  /**
   * Apply common filters to the query builder helper
   * This method should be overridden by subclasses to add specific filters
   * @param queryBuilderHelper QueryBuilderHelper instance
   */
  applyFilters<T extends object>(
    queryBuilderHelper: QueryBuilderHelper<T>,
  ): void {
    const filter = this.buildFilters();
    if (filter) {
      queryBuilderHelper.applyFilter(filter);
    }
  }

  /**
   * Apply sorting to the query builder helper
   * @param queryBuilderHelper QueryBuilderHelper instance
   * @param sortBy Field to sort by
   */
  applySorting<T extends object>(
    queryBuilderHelper: QueryBuilderHelper<T>,
    sortBy: string,
  ): void {
    queryBuilderHelper.applySorting(sortBy, this.sortOrder || SortOrder.ASC);
  }

  /**
   * Apply pagination to the query builder helper
   * @param queryBuilderHelper QueryBuilderHelper instance
   */
  applyPagination<T extends object>(
    queryBuilderHelper: QueryBuilderHelper<T>,
  ): void {
    queryBuilderHelper.applyPagination(this);
  }

  /**
   * Apply all filters, sorting and pagination to the query builder helper
   * @param queryBuilderHelper QueryBuilderHelper instance
   * @param sortBy Field to sort by
   */
  apply<T extends object>(
    queryBuilderHelper: QueryBuilderHelper<T>,
    sortBy: string,
  ): void {
    this.applyFilters(queryBuilderHelper);
    this.applySorting(queryBuilderHelper, sortBy);
    this.applyPagination(queryBuilderHelper);
  }

  /**
   * Create a filter builder
   * @returns FilterBuilder instance
   */
  createFilterBuilder(): FilterBuilder {
    return new FilterBuilder();
  }
}
