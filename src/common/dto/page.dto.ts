import { ApiProperty } from '@nestjs/swagger';
import { PageMetaDto } from './page-meta.dto';

export class PageDto<T> {
  @ApiProperty({
    description: 'Array of items',
    isArray: true,
  })
  readonly data: T[];

  @ApiProperty({
    description: 'Page metadata',
    type: PageMetaDto,
  })
  readonly meta: PageMetaDto;

  constructor(data: T[], meta: PageMetaDto) {
    this.data = data;
    this.meta = meta;
  }
}
