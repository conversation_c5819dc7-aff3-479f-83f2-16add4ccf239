import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationDto } from './pagination.dto';

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum FilterOperator {
  EQ = 'eq',
  NE = 'ne',
  GT = 'gt',
  GTE = 'gte',
  LT = 'lt',
  LTE = 'lte',
  IN = 'in',
  NOT_IN = 'notIn',
  LIKE = 'like',
  ILIKE = 'ilike',
  STARTS_WITH = 'startsWith',
  ENDS_WITH = 'endsWith',
  IS_NULL = 'isNull',
  IS_NOT_NULL = 'isNotNull',
  BETWEEN = 'between',
}

export enum LogicalOperator {
  AND = 'and',
  OR = 'or',
}

export class FilterCondition {
  @ApiPropertyOptional({
    description: 'Field name',
    example: 'name',
  })
  @IsString()
  field: string;

  @ApiPropertyOptional({
    description: 'Filter operator',
    enum: FilterOperator,
    example: FilterOperator.EQ,
  })
  @IsEnum(FilterOperator)
  operator: FilterOperator;

  @ApiPropertyOptional({
    description: 'Filter value',
    example: 'John',
  })
  value: any;
}

export class FilterGroup {
  @ApiPropertyOptional({
    description: 'Logical operator',
    enum: LogicalOperator,
    default: LogicalOperator.AND,
  })
  @IsEnum(LogicalOperator)
  @IsOptional()
  operator?: LogicalOperator = LogicalOperator.AND;

  @ApiPropertyOptional({
    description: 'Filter conditions',
    type: [FilterCondition],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterCondition)
  @IsOptional()
  conditions?: FilterCondition[] = [];

  @ApiPropertyOptional({
    description: 'Nested filter groups',
    type: [FilterGroup],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterGroup)
  @IsOptional()
  groups?: FilterGroup[] = [];
}

export class SortCriterion {
  @ApiPropertyOptional({
    description: 'Field to sort by',
    example: 'name',
  })
  @IsString()
  field: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: SortOrder,
    default: SortOrder.ASC,
  })
  @IsEnum(SortOrder)
  @IsOptional()
  order?: SortOrder = SortOrder.ASC;

  @ApiPropertyOptional({
    description: 'Field alias',
    example: 'user',
  })
  @IsString()
  @IsOptional()
  alias?: string;
}

export class AdvancedFilterDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Filter group',
    type: FilterGroup,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => FilterGroup)
  @IsOptional()
  filter?: FilterGroup;

  @ApiPropertyOptional({
    description: 'Sort criteria',
    type: [SortCriterion],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SortCriterion)
  @IsOptional()
  sort?: SortCriterion[] = [];

  @ApiPropertyOptional({
    description: 'Search term for full-text search',
    example: 'keyword',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Fields to search in for full-text search',
    example: ['name', 'description'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  searchFields?: string[] = [];

  @ApiPropertyOptional({
    description: 'Fields to select in the response',
    example: ['id', 'name', 'price'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  select?: string[] = [];

  @ApiPropertyOptional({
    description: 'Whether to return raw results',
    example: false,
  })
  @IsOptional()
  raw?: boolean = false;
}
