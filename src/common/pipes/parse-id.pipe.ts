import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';

/**
 * Parse ID pipe
 * This pipe is used to parse ID parameters from string to number
 * It is used in controllers to validate and transform ID parameters
 */
@Injectable()
export class ParseIdPipe implements PipeTransform<string, number> {
  transform(value: string): number {
    const id = Number(value);
    if (isNaN(id)) {
      throw new BadRequestException('ID must be a number');
    }
    return id;
  }
}
