import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder, Brackets, ObjectLiteral } from 'typeorm';
import { PaginationDto } from '@common/dto/pagination.dto';
import { FilterBuilder, IFilter } from '@common/database/filters';

/**
 * QueryBuilderService
 * Service to handle query building with security in mind
 */
@Injectable()
export class QueryBuilderService {
  /**
   * Apply pagination to a query builder
   * @param queryBuilder TypeORM query builder
   * @param paginationDto Pagination DTO
   * @returns Query builder with pagination applied
   */
  applyPagination<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    paginationDto: PaginationDto,
  ): SelectQueryBuilder<T> {
    const page = paginationDto.page || 1;
    const limit = paginationDto.limit || 10;
    const skip = (page - 1) * limit;

    return queryBuilder.skip(skip).take(limit);
  }

  /**
   * Apply cursor-based pagination to a query builder
   * @param queryBuilder TypeORM query builder
   * @param cursorField Field to use as cursor
   * @param cursorValue Cursor value
   * @param limit Number of items to take
   * @param isAfter Whether to get items after the cursor
   * @param alias Table alias
   * @returns Query builder with cursor-based pagination applied
   */
  applyCursorPagination<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    cursorField: string,
    cursorValue: any,
    limit: number = 10,
    isAfter: boolean = true,
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (cursorValue !== undefined && cursorValue !== null) {
      const operator = isAfter ? '>' : '<';
      queryBuilder.andWhere(
        `${alias}.${cursorField} ${operator} :cursorValue`,
        {
          cursorValue,
        },
      );
    }

    const sortOrder = isAfter ? 'ASC' : 'DESC';
    queryBuilder.orderBy(`${alias}.${cursorField}`, sortOrder);

    return queryBuilder.take(limit);
  }

  /**
   * Apply sorting to a query builder
   * @param queryBuilder TypeORM query builder
   * @param sortBy Field to sort by
   * @param sortOrder Sort order (ASC or DESC)
   * @param alias Table alias
   * @returns Query builder with sorting applied
   */
  applySorting<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    sortBy: string,
    sortOrder: 'ASC' | 'DESC',
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    return queryBuilder.orderBy(`${alias}.${sortBy}`, sortOrder);
  }

  /**
   * Apply multiple sorting criteria to a query builder
   * @param queryBuilder TypeORM query builder
   * @param sortCriteria Array of sort criteria
   * @returns Query builder with multiple sorting criteria applied
   */
  applyMultipleSorting<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    sortCriteria: Array<{
      field: string;
      order: 'ASC' | 'DESC';
      alias?: string;
    }>,
  ): SelectQueryBuilder<T> {
    if (!sortCriteria || !sortCriteria.length) {
      return queryBuilder;
    }

    // Apply the first sort criterion using orderBy
    const firstCriterion = sortCriteria[0];
    const firstAlias = firstCriterion.alias || 'entity';
    queryBuilder.orderBy(
      `${firstAlias}.${firstCriterion.field}`,
      firstCriterion.order,
    );

    // Apply the rest using addOrderBy
    for (let i = 1; i < sortCriteria.length; i++) {
      const criterion = sortCriteria[i];
      const alias = criterion.alias || 'entity';
      queryBuilder.addOrderBy(`${alias}.${criterion.field}`, criterion.order);
    }

    return queryBuilder;
  }

  /**
   * Apply sorting on relation field
   * @param queryBuilder TypeORM query builder
   * @param relation Relation name
   * @param relationAlias Relation alias
   * @param field Field in the relation to sort by
   * @param sortOrder Sort order (ASC or DESC)
   * @returns Query builder with relation sorting applied
   */
  applyRelationSorting<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    relation: string,
    relationAlias: string,
    field: string,
    sortOrder: 'ASC' | 'DESC',
  ): SelectQueryBuilder<T> {
    // Join the relation if not already joined
    this.ensureJoin(queryBuilder, relation, relationAlias);

    // Apply sorting
    return queryBuilder.orderBy(`${relationAlias}.${field}`, sortOrder);
  }

  /**
   * Apply a filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param filter Filter to apply
   * @param alias Table alias
   * @returns Query builder with filter applied
   */
  applyFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    filter: IFilter,
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (filter) {
      filter.apply(queryBuilder, alias);
    }
    return queryBuilder;
  }

  /**
   * Apply exact match filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param field Field name
   * @param value Field value
   * @param alias Table alias
   * @returns Query builder with filter applied
   */
  applyExactMatchFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    field: string,
    value: any,
    alias: string = 'entity',
    operator: string = '=',
  ): SelectQueryBuilder<T> {
    if (value === undefined || value === null || value === '') {
      return queryBuilder;
    }

    const paramName = `${field}_${operator.replace(/[^a-zA-Z0-9]/g, '')}`;
    const fieldName = `${alias}.${field}`;

    return queryBuilder.andWhere(`${fieldName} ${operator} :${paramName}`, {
      [paramName]: value,
    });
  }

  /**
   * Apply partial match filter to a query builder (ILIKE)
   * @param queryBuilder TypeORM query builder
   * @param field Field name
   * @param value Field value
   * @param alias Table alias
   * @returns Query builder with filter applied
   */
  applyPartialMatchFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    field: string,
    value: string,
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (!value) {
      return queryBuilder;
    }

    const paramName = `${field}_ilike`;
    const fieldName = `${alias}.${field}`;

    return queryBuilder.andWhere(`${fieldName} ILIKE :${paramName}`, {
      [paramName]: `%${value}%`,
    });
  }

  /**
   * Apply date range filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param field Field name
   * @param startDate Start date
   * @param endDate End date
   * @param alias Table alias
   * @returns Query builder with filter applied
   */
  applyDateRangeFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    field: string,
    startDate?: Date,
    endDate?: Date,
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (!startDate && !endDate) {
      return queryBuilder;
    }

    const fieldName = `${alias}.${field}`;

    if (startDate) {
      queryBuilder.andWhere(`${fieldName} >= :${field}Start`, {
        [`${field}Start`]: startDate,
      });
    }

    if (endDate) {
      queryBuilder.andWhere(`${fieldName} <= :${field}End`, {
        [`${field}End`]: endDate,
      });
    }

    return queryBuilder;
  }

  /**
   * Apply number range filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param field Field name
   * @param min Minimum value
   * @param max Maximum value
   * @param alias Table alias
   * @returns Query builder with filter applied
   */
  applyNumberRangeFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    field: string,
    min?: number,
    max?: number,
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (min === undefined && max === undefined) {
      return queryBuilder;
    }

    const fieldName = `${alias}.${field}`;

    if (min !== undefined) {
      queryBuilder.andWhere(`${fieldName} >= :${field}Min`, {
        [`${field}Min`]: min,
      });
    }

    if (max !== undefined) {
      queryBuilder.andWhere(`${fieldName} <= :${field}Max`, {
        [`${field}Max`]: max,
      });
    }

    return queryBuilder;
  }

  /**
   * Apply OR conditions to a query builder
   * @param queryBuilder TypeORM query builder
   * @param conditions Array of callback functions that apply conditions
   * @returns Query builder with OR conditions applied
   */
  applyOrConditions<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    conditions: ((qb: SelectQueryBuilder<T>) => void)[],
  ): SelectQueryBuilder<T> {
    if (!conditions || conditions.length === 0) {
      return queryBuilder;
    }

    return queryBuilder.andWhere(
      new Brackets((qb) => {
        conditions.forEach((condition, index) => {
          if (index === 0) {
            // Create a new query builder for the first condition
            const subQb = queryBuilder.connection.createQueryBuilder();
            condition(subQb);
            // Extract the where clause
            const whereClause = this.extractWhereClause(subQb);
            if (whereClause) {
              qb.where(whereClause, subQb.getParameters());
            }
          } else {
            // Create a new query builder for subsequent conditions
            const subQb = queryBuilder.connection.createQueryBuilder();
            condition(subQb);
            // Extract the where clause
            const whereClause = this.extractWhereClause(subQb);
            if (whereClause) {
              qb.orWhere(whereClause, subQb.getParameters());
            }
          }
        });
      }),
    );
  }

  /**
   * Apply AND conditions to a query builder
   * @param queryBuilder TypeORM query builder
   * @param conditions Array of callback functions that apply conditions
   * @returns Query builder with AND conditions applied
   */
  applyAndConditions<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    conditions: ((qb: SelectQueryBuilder<T>) => void)[],
  ): SelectQueryBuilder<T> {
    if (!conditions || conditions.length === 0) {
      return queryBuilder;
    }

    return queryBuilder.andWhere(
      new Brackets((qb) => {
        conditions.forEach((condition, index) => {
          if (index === 0) {
            // Create a new query builder for the first condition
            const subQb = queryBuilder.connection.createQueryBuilder();
            condition(subQb);
            // Extract the where clause
            const whereClause = this.extractWhereClause(subQb);
            if (whereClause) {
              qb.where(whereClause, subQb.getParameters());
            }
          } else {
            // Create a new query builder for subsequent conditions
            const subQb = queryBuilder.connection.createQueryBuilder();
            condition(subQb);
            // Extract the where clause
            const whereClause = this.extractWhereClause(subQb);
            if (whereClause) {
              qb.andWhere(whereClause, subQb.getParameters());
            }
          }
        });
      }),
    );
  }

  /**
   * Apply NOT condition to a query builder
   * @param queryBuilder TypeORM query builder
   * @param condition Callback function that applies condition
   * @returns Query builder with NOT condition applied
   */
  applyNotCondition<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    condition: (qb: SelectQueryBuilder<T>) => void,
  ): SelectQueryBuilder<T> {
    // Create a new query builder for the condition
    const subQb = queryBuilder.connection.createQueryBuilder();
    condition(subQb);

    // Extract the where clause
    const whereClause = this.extractWhereClause(subQb);
    if (whereClause) {
      return queryBuilder.andWhere(
        `NOT (${whereClause})`,
        subQb.getParameters(),
      );
    }

    return queryBuilder;
  }

  /**
   * Extract where clause from a query builder
   * @param queryBuilder TypeORM query builder
   * @returns Where clause string or null
   */
  private extractWhereClause<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
  ): string | null {
    const query = queryBuilder.getQuery();
    const whereMatch = query.match(
      /WHERE\s+(.*?)(?:ORDER BY|GROUP BY|LIMIT|$)/i,
    );

    if (whereMatch && whereMatch[1]) {
      return whereMatch[1].trim();
    }

    return null;
  }

  /**
   * Apply filter on relation
   * @param queryBuilder TypeORM query builder
   * @param relation Relation name
   * @param relationAlias Relation alias
   * @param filterCallback Callback function that applies filter on relation
   * @returns Query builder with relation filter applied
   */
  applyRelationFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    relation: string,
    relationAlias: string,
    filterCallback: (qb: SelectQueryBuilder<any>) => void,
  ): SelectQueryBuilder<T> {
    // Join the relation if not already joined
    this.ensureJoin(queryBuilder, relation, relationAlias);

    // Apply the filter
    filterCallback(queryBuilder);

    return queryBuilder;
  }

  /**
   * Apply exists filter on relation
   * @param queryBuilder TypeORM query builder
   * @param entityAlias Main entity alias
   * @param relation Relation entity
   * @param relationAlias Relation alias
   * @param joinColumn Join column
   * @param filterCallback Callback function that applies filter on relation
   * @returns Query builder with exists filter applied
   */
  applyExistsFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    entityAlias: string,
    relation: any,
    relationAlias: string,
    joinColumn: string,
    filterCallback: (qb: SelectQueryBuilder<any>) => void,
  ): SelectQueryBuilder<T> {
    // Create a subquery
    const subQuery = queryBuilder.connection
      .createQueryBuilder()
      .select('1')
      .from(relation, relationAlias)
      .where(`${relationAlias}.${joinColumn} = ${entityAlias}.id`);

    // Apply the filter to the subquery
    filterCallback(subQuery);

    // Apply EXISTS to the main query
    return queryBuilder.andWhere(`EXISTS (${subQuery.getQuery()})`);
  }

  /**
   * Apply not exists filter on relation
   * @param queryBuilder TypeORM query builder
   * @param entityAlias Main entity alias
   * @param relation Relation entity
   * @param relationAlias Relation alias
   * @param joinColumn Join column
   * @param filterCallback Callback function that applies filter on relation
   * @returns Query builder with not exists filter applied
   */
  applyNotExistsFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    entityAlias: string,
    relation: any,
    relationAlias: string,
    joinColumn: string,
    filterCallback: (qb: SelectQueryBuilder<any>) => void,
  ): SelectQueryBuilder<T> {
    // Create a subquery
    const subQuery = queryBuilder.connection
      .createQueryBuilder()
      .select('1')
      .from(relation, relationAlias)
      .where(`${relationAlias}.${joinColumn} = ${entityAlias}.id`);

    // Apply the filter to the subquery
    filterCallback(subQuery);

    // Apply NOT EXISTS to the main query
    return queryBuilder.andWhere(`NOT EXISTS (${subQuery.getQuery()})`);
  }

  /**
   * Ensure that a relation is joined
   * @param queryBuilder TypeORM query builder
   * @param relation Relation name
   * @param alias Alias for the joined relation
   * @returns Alias for the joined relation
   */
  private ensureJoin<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    relation: string,
    alias: string,
  ): string {
    // Check if the relation is already joined
    const joins = (queryBuilder as any).expressionMap.joinAttributes;
    const isJoined = joins.some((join: any) => join.alias === alias);

    if (!isJoined) {
      // Join the relation
      queryBuilder.leftJoinAndSelect(relation, alias);
    }

    return alias;
  }

  /**
   * Apply custom filter with raw SQL
   * @param queryBuilder TypeORM query builder
   * @param sql SQL condition
   * @param parameters SQL parameters
   * @returns Query builder with custom filter applied
   */
  applyCustomFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    sql: string,
    parameters: Record<string, any> = {},
  ): SelectQueryBuilder<T> {
    return queryBuilder.andWhere(sql, parameters);
  }

  /**
   * Apply full-text search filter
   * @param queryBuilder TypeORM query builder
   * @param fields Array of fields to search
   * @param searchTerm Search term
   * @param alias Table alias
   * @returns Query builder with full-text search filter applied
   */
  applyFullTextSearchFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    fields: string[],
    searchTerm: string,
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (!searchTerm || !fields.length) {
      return queryBuilder;
    }

    // Create a condition for each field
    const conditions = fields.map((field) => {
      return `${alias}.${field} ILIKE :searchTerm`;
    });

    // Join conditions with OR
    return queryBuilder.andWhere(`(${conditions.join(' OR ')})`, {
      searchTerm: `%${searchTerm}%`,
    });
  }

  /**
   * Apply JSON field filter
   * @param queryBuilder TypeORM query builder
   * @param field JSON field name
   * @param jsonPath JSON path
   * @param value Value to compare
   * @param operator Comparison operator
   * @param alias Table alias
   * @returns Query builder with JSON field filter applied
   */
  applyJsonFieldFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    field: string,
    jsonPath: string,
    value: any,
    operator: string = '=',
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (value === undefined || value === null) {
      return queryBuilder;
    }

    const paramName = `${field}_${jsonPath.replace(/\./g, '_')}_json`;
    const fieldName = `${alias}.${field}->>'${jsonPath}'`;

    return queryBuilder.andWhere(`${fieldName} ${operator} :${paramName}`, {
      [paramName]: value,
    });
  }

  /**
   * Apply array contains filter
   * @param queryBuilder TypeORM query builder
   * @param field Array field name
   * @param value Value to check
   * @param alias Table alias
   * @returns Query builder with array contains filter applied
   */
  applyArrayContainsFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    field: string,
    value: any,
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (value === undefined || value === null) {
      return queryBuilder;
    }

    const paramName = `${field}_contains`;
    const fieldName = `${alias}.${field}`;

    return queryBuilder.andWhere(`${fieldName} @> ARRAY[:${paramName}]`, {
      [paramName]: value,
    });
  }

  /**
   * Apply select to a query builder
   * @param queryBuilder TypeORM query builder
   * @param fields Array of fields to select
   * @param alias Table alias
   * @returns Query builder with select applied
   */
  applySelect<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    fields: string[],
    alias: string = 'entity',
  ): SelectQueryBuilder<T> {
    if (!fields || !fields.length) {
      return queryBuilder;
    }

    // Map fields to include alias
    const fieldsWithAlias = fields.map((field) => `${alias}.${field}`);

    return queryBuilder.select(fieldsWithAlias);
  }

  /**
   * Get count with separate query
   * @param queryBuilder TypeORM query builder
   * @returns Promise with count
   */
  async getCount<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
  ): Promise<number> {
    // Clone the query builder to create a count query
    const countQueryBuilder = queryBuilder.clone();

    // Remove select, order by, limit, offset
    countQueryBuilder
      .skip(undefined)
      .take(undefined)
      .orderBy()
      .select('COUNT(DISTINCT ' + countQueryBuilder.alias + '.id)', 'count');

    // Execute count query
    const { count } = await countQueryBuilder.getRawOne();

    return Number(count);
  }

  /**
   * Get many and count with separate queries
   * @param queryBuilder TypeORM query builder
   * @returns Promise with results and count
   */
  async getManyAndCount<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
  ): Promise<[T[], number]> {
    // Get count with separate query
    const count = await this.getCount(queryBuilder);

    // Get results
    const results = await queryBuilder.getMany();

    return [results, count];
  }

  /**
   * Get raw many and count with separate queries
   * @param queryBuilder TypeORM query builder
   * @returns Promise with raw results and count
   */
  async getRawManyAndCount<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
  ): Promise<[any[], number]> {
    // Get count with separate query
    const count = await this.getCount(queryBuilder);

    // Get raw results
    const results = await queryBuilder.getRawMany();

    return [results, count];
  }

  /**
   * Create a filter builder
   * @returns FilterBuilder instance
   */
  createFilterBuilder(): FilterBuilder {
    return new FilterBuilder();
  }
}
