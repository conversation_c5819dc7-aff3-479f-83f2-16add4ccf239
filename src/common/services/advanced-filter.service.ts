import { Injectable } from '@nestjs/common';
import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import {
  AdvancedFilterDto,
  FilterCondition,
  FilterGroup,
  FilterOperator,
  LogicalOperator,
} from '@common/dto/advanced-filter.dto';
import { QueryBuilderService } from './query-builder.service';

/**
 * AdvancedFilterService
 * Service to handle advanced filtering with complex conditions
 */
@Injectable()
export class AdvancedFilterService {
  constructor(private queryBuilderService: QueryBuilderService) {}

  /**
   * Apply advanced filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param advancedFilterDto Advanced filter DTO
   * @param entityAlias Entity alias
   * @returns Query builder with advanced filter applied
   */
  applyAdvancedFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    advancedFilterDto: AdvancedFilterDto,
    entityAlias: string = 'entity',
  ): SelectQueryBuilder<T> {
    // Apply select if specified
    if (advancedFilterDto.select?.length) {
      // Always include id for proper entity loading
      const selectFields = [...new Set([...advancedFilterDto.select, 'id'])];
      this.queryBuilderService.applySelect(
        queryBuilder,
        selectFields,
        entityAlias,
      );
    }

    // Apply filter group
    if (advancedFilterDto.filter) {
      this.applyFilterGroup(
        queryBuilder,
        advancedFilterDto.filter,
        entityAlias,
      );
    }

    // Apply full-text search
    if (advancedFilterDto.search && advancedFilterDto.searchFields?.length) {
      this.queryBuilderService.applyFullTextSearchFilter(
        queryBuilder,
        advancedFilterDto.searchFields,
        advancedFilterDto.search,
        entityAlias,
      );
    }

    // Apply sorting
    if (advancedFilterDto.sort?.length) {
      // Convert SortOrder enum to string literals 'ASC' | 'DESC'
      const sortCriteria = advancedFilterDto.sort.map((item) => ({
        field: item.field,
        order: item.order as 'ASC' | 'DESC',
        alias: item.alias,
      }));

      this.queryBuilderService.applyMultipleSorting(queryBuilder, sortCriteria);
    }

    // Apply pagination
    this.queryBuilderService.applyPagination(queryBuilder, advancedFilterDto);

    return queryBuilder;
  }

  /**
   * Get results with advanced filter
   * @param queryBuilder TypeORM query builder
   * @param advancedFilterDto Advanced filter DTO
   * @param entityAlias Entity alias
   * @returns Promise with results and count
   */
  async getResultsWithAdvancedFilter<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    advancedFilterDto: AdvancedFilterDto,
    entityAlias: string = 'entity',
  ): Promise<[any[], number]> {
    // Apply advanced filter
    this.applyAdvancedFilter(queryBuilder, advancedFilterDto, entityAlias);

    // Get results and count
    if (advancedFilterDto.raw) {
      // Get raw results and count
      return this.queryBuilderService.getRawManyAndCount(queryBuilder);
    } else {
      // Get entity results and count
      return this.queryBuilderService.getManyAndCount(queryBuilder);
    }
  }

  /**
   * Apply filter group to a query builder
   * @param queryBuilder TypeORM query builder
   * @param filterGroup Filter group
   * @param entityAlias Entity alias
   * @returns Query builder with filter group applied
   */
  private applyFilterGroup<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    filterGroup: FilterGroup,
    entityAlias: string,
  ): SelectQueryBuilder<T> {
    const {
      operator = LogicalOperator.AND,
      conditions = [],
      groups = [],
    } = filterGroup;

    // Process conditions
    if (conditions.length > 0) {
      const conditionCallbacks = conditions.map((condition) => {
        return (qb: SelectQueryBuilder<T>) => {
          this.applyFilterCondition(qb, condition, entityAlias);
        };
      });

      if (operator === LogicalOperator.AND) {
        conditionCallbacks.forEach((callback) => callback(queryBuilder));
      } else {
        this.queryBuilderService.applyOrConditions(
          queryBuilder,
          conditionCallbacks,
        );
      }
    }

    // Process nested groups
    if (groups.length > 0) {
      const groupCallbacks = groups.map((group) => {
        return (qb: SelectQueryBuilder<T>) => {
          this.applyFilterGroup(qb, group, entityAlias);
        };
      });

      if (operator === LogicalOperator.AND) {
        this.queryBuilderService.applyAndConditions(
          queryBuilder,
          groupCallbacks,
        );
      } else {
        this.queryBuilderService.applyOrConditions(
          queryBuilder,
          groupCallbacks,
        );
      }
    }

    return queryBuilder;
  }

  /**
   * Apply filter condition to a query builder
   * @param queryBuilder TypeORM query builder
   * @param condition Filter condition
   * @param entityAlias Entity alias
   * @returns Query builder with filter condition applied
   */
  private applyFilterCondition<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    condition: FilterCondition,
    entityAlias: string,
  ): SelectQueryBuilder<T> {
    const { field, operator, value } = condition;

    switch (operator) {
      case FilterOperator.EQ:
        return this.queryBuilderService.applyExactMatchFilter(
          queryBuilder,
          field,
          value,
          entityAlias,
        );

      case FilterOperator.NE:
        return this.queryBuilderService.applyExactMatchFilter(
          queryBuilder,
          field,
          value,
          entityAlias,
          '!=',
        );

      case FilterOperator.GT:
        return this.queryBuilderService.applyExactMatchFilter(
          queryBuilder,
          field,
          value,
          entityAlias,
          '>',
        );

      case FilterOperator.GTE:
        return this.queryBuilderService.applyExactMatchFilter(
          queryBuilder,
          field,
          value,
          entityAlias,
          '>=',
        );

      case FilterOperator.LT:
        return this.queryBuilderService.applyExactMatchFilter(
          queryBuilder,
          field,
          value,
          entityAlias,
          '<',
        );

      case FilterOperator.LTE:
        return this.queryBuilderService.applyExactMatchFilter(
          queryBuilder,
          field,
          value,
          entityAlias,
          '<=',
        );

      case FilterOperator.IN:
        return this.queryBuilderService.applyCustomFilter(
          queryBuilder,
          `${entityAlias}.${field} IN (:...${field}_in)`,
          { [`${field}_in`]: value },
        );

      case FilterOperator.NOT_IN:
        return this.queryBuilderService.applyCustomFilter(
          queryBuilder,
          `${entityAlias}.${field} NOT IN (:...${field}_notIn)`,
          { [`${field}_notIn`]: value },
        );

      case FilterOperator.LIKE:
        return this.queryBuilderService.applyCustomFilter(
          queryBuilder,
          `${entityAlias}.${field} LIKE :${field}_like`,
          { [`${field}_like`]: `%${value}%` },
        );

      case FilterOperator.ILIKE:
        return this.queryBuilderService.applyPartialMatchFilter(
          queryBuilder,
          field,
          value,
          entityAlias,
        );

      case FilterOperator.STARTS_WITH:
        return this.queryBuilderService.applyCustomFilter(
          queryBuilder,
          `${entityAlias}.${field} LIKE :${field}_startsWith`,
          { [`${field}_startsWith`]: `${value}%` },
        );

      case FilterOperator.ENDS_WITH:
        return this.queryBuilderService.applyCustomFilter(
          queryBuilder,
          `${entityAlias}.${field} LIKE :${field}_endsWith`,
          { [`${field}_endsWith`]: `%${value}` },
        );

      case FilterOperator.IS_NULL:
        return this.queryBuilderService.applyCustomFilter(
          queryBuilder,
          `${entityAlias}.${field} IS NULL`,
        );

      case FilterOperator.IS_NOT_NULL:
        return this.queryBuilderService.applyCustomFilter(
          queryBuilder,
          `${entityAlias}.${field} IS NOT NULL`,
        );

      case FilterOperator.BETWEEN:
        if (Array.isArray(value) && value.length === 2) {
          return this.queryBuilderService.applyCustomFilter(
            queryBuilder,
            `${entityAlias}.${field} BETWEEN :${field}_start AND :${field}_end`,
            {
              [`${field}_start`]: value[0],
              [`${field}_end`]: value[1],
            },
          );
        }
        return queryBuilder;

      default:
        return queryBuilder;
    }
  }
}
