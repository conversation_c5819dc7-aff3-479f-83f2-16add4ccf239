import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import { Filter } from './filter';

/**
 * Equal Filter
 * Filters records where field equals value
 */
export class EqualFilter extends Filter {
  private field: string;
  private value: any;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: any) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T> {
    if (this.value === undefined || this.value === null) {
      return queryBuilder;
    }

    const paramName = `${this.field}_equal`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;

    return queryBuilder.andWhere(`${fieldName} = :${paramName}`, {
      [paramName]: this.value,
    });
  }
}

/**
 * Not Equal Filter
 * Filters records where field does not equal value
 */
export class NotEqualFilter extends Filter {
  private field: string;
  private value: any;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: any) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T> {
    if (this.value === undefined || this.value === null) {
      return queryBuilder;
    }

    const paramName = `${this.field}_notEqual`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;

    return queryBuilder.andWhere(`${fieldName} != :${paramName}`, {
      [paramName]: this.value,
    });
  }
}

/**
 * In Filter
 * Filters records where field is in values
 */
export class InFilter extends Filter {
  private field: string;
  private values: any[];

  /**
   * Constructor
   * @param field Field name
   * @param values Array of values
   */
  constructor(field: string, values: any[]) {
    super();
    this.field = field;
    this.values = values;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T> {
    if (!this.values || this.values.length === 0) {
      return queryBuilder;
    }

    const paramName = `${this.field}_in`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;

    return queryBuilder.andWhere(`${fieldName} IN (:...${paramName})`, {
      [paramName]: this.values,
    });
  }
}

/**
 * Not In Filter
 * Filters records where field is not in values
 */
export class NotInFilter extends Filter {
  private field: string;
  private values: any[];

  /**
   * Constructor
   * @param field Field name
   * @param values Array of values
   */
  constructor(field: string, values: any[]) {
    super();
    this.field = field;
    this.values = values;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T> {
    if (!this.values || this.values.length === 0) {
      return queryBuilder;
    }

    const paramName = `${this.field}_notIn`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;

    return queryBuilder.andWhere(`${fieldName} NOT IN (:...${paramName})`, {
      [paramName]: this.values,
    });
  }
}
