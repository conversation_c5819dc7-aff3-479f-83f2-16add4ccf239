import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import { Filter } from './filter';

/**
 * Greater Than Filter
 * Filters records where field is greater than value
 */
export class GreaterThanFilter extends Filter {
  private field: string;
  private value: number | Date;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: number | Date) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if (this.value === undefined || this.value === null) {
      return queryBuilder;
    }

    const paramName = `${this.field}_gt`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    return queryBuilder.andWhere(`${fieldName} > :${paramName}`, {
      [paramName]: this.value,
    });
  }
}

/**
 * Greater Than or Equal Filter
 * Filters records where field is greater than or equal to value
 */
export class GreaterThanOrEqualFilter extends Filter {
  private field: string;
  private value: number | Date;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: number | Date) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if (this.value === undefined || this.value === null) {
      return queryBuilder;
    }

    const paramName = `${this.field}_gte`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    return queryBuilder.andWhere(`${fieldName} >= :${paramName}`, {
      [paramName]: this.value,
    });
  }
}

/**
 * Less Than Filter
 * Filters records where field is less than value
 */
export class LessThanFilter extends Filter {
  private field: string;
  private value: number | Date;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: number | Date) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if (this.value === undefined || this.value === null) {
      return queryBuilder;
    }

    const paramName = `${this.field}_lt`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    return queryBuilder.andWhere(`${fieldName} < :${paramName}`, {
      [paramName]: this.value,
    });
  }
}

/**
 * Less Than or Equal Filter
 * Filters records where field is less than or equal to value
 */
export class LessThanOrEqualFilter extends Filter {
  private field: string;
  private value: number | Date;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: number | Date) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if (this.value === undefined || this.value === null) {
      return queryBuilder;
    }

    const paramName = `${this.field}_lte`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    return queryBuilder.andWhere(`${fieldName} <= :${paramName}`, {
      [paramName]: this.value,
    });
  }
}

/**
 * Between Filter
 * Filters records where field is between min and max values
 */
export class BetweenFilter extends Filter {
  private field: string;
  private min: number | Date;
  private max: number | Date;

  /**
   * Constructor
   * @param field Field name
   * @param min Minimum value
   * @param max Maximum value
   */
  constructor(field: string, min: number | Date, max: number | Date) {
    super();
    this.field = field;
    this.min = min;
    this.max = max;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if ((this.min === undefined || this.min === null) && 
        (this.max === undefined || this.max === null)) {
      return queryBuilder;
    }

    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    if (this.min !== undefined && this.min !== null && 
        this.max !== undefined && this.max !== null) {
      return queryBuilder.andWhere(`${fieldName} BETWEEN :min AND :max`, {
        min: this.min,
        max: this.max,
      });
    } else if (this.min !== undefined && this.min !== null) {
      return queryBuilder.andWhere(`${fieldName} >= :min`, { min: this.min });
    } else if (this.max !== undefined && this.max !== null) {
      return queryBuilder.andWhere(`${fieldName} <= :max`, { max: this.max });
    }

    return queryBuilder;
  }
}
