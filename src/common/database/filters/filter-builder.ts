import { CompositeFilter, Filter, IFilter } from './filter';
import {
  EqualFilter,
  NotEqualFilter,
  InFilter,
  NotInFilter,
} from './comparison-filters';
import {
  <PERSON><PERSON>hanFilter,
  GreaterThanOr<PERSON>qual<PERSON>ilter,
  <PERSON><PERSON><PERSON><PERSON>ilter,
  <PERSON><PERSON>han<PERSON>r<PERSON>qualFilter,
  BetweenFilter,
} from './range-filters';
import {
  LikeFilter,
  ILikeFilter,
  StartsWithFilter,
  EndsWithFilter,
} from './string-filters';
import { IsNullFilter, IsNotNullFilter } from './null-filters';

/**
 * FilterBuilder class
 * Used to build complex filters
 */
export class FilterBuilder {
  private filters: IFilter[] = [];

  /**
   * Add a filter to the builder
   * @param filter Filter to add
   */
  add(filter: IFilter): FilterBuilder {
    this.filters.push(filter);
    return this;
  }

  /**
   * Create a composite filter with AND operator
   * @param filters Array of filters to combine
   */
  and(filters: IFilter[]): FilterBuilder {
    this.filters.push(new CompositeFilter(filters, 'AND'));
    return this;
  }

  /**
   * Create a composite filter with OR operator
   * @param filters Array of filters to combine
   */
  or(filters: IFilter[]): FilterBuilder {
    this.filters.push(new CompositeFilter(filters, 'OR'));
    return this;
  }

  /**
   * Add an equal filter
   * @param field Field name
   * @param value Field value
   */
  equal(field: string, value: any): FilterBuilder {
    return this.add(new EqualFilter(field, value));
  }

  /**
   * Add a not equal filter
   * @param field Field name
   * @param value Field value
   */
  notEqual(field: string, value: any): FilterBuilder {
    return this.add(new NotEqualFilter(field, value));
  }

  /**
   * Add an in filter
   * @param field Field name
   * @param values Array of values
   */
  in(field: string, values: any[]): FilterBuilder {
    return this.add(new InFilter(field, values));
  }

  /**
   * Add a not in filter
   * @param field Field name
   * @param values Array of values
   */
  notIn(field: string, values: any[]): FilterBuilder {
    return this.add(new NotInFilter(field, values));
  }

  /**
   * Add a greater than filter
   * @param field Field name
   * @param value Field value
   */
  greaterThan(field: string, value: number | Date): FilterBuilder {
    return this.add(new GreaterThanFilter(field, value));
  }

  /**
   * Add a greater than or equal filter
   * @param field Field name
   * @param value Field value
   */
  greaterThanOrEqual(field: string, value: number | Date): FilterBuilder {
    return this.add(new GreaterThanOrEqualFilter(field, value));
  }

  /**
   * Add a less than filter
   * @param field Field name
   * @param value Field value
   */
  lessThan(field: string, value: number | Date): FilterBuilder {
    return this.add(new LessThanFilter(field, value));
  }

  /**
   * Add a less than or equal filter
   * @param field Field name
   * @param value Field value
   */
  lessThanOrEqual(field: string, value: number | Date): FilterBuilder {
    return this.add(new LessThanOrEqualFilter(field, value));
  }

  /**
   * Add a between filter
   * @param field Field name
   * @param min Minimum value
   * @param max Maximum value
   */
  between(
    field: string,
    min: number | Date,
    max: number | Date,
  ): FilterBuilder {
    return this.add(new BetweenFilter(field, min, max));
  }

  /**
   * Add a like filter
   * @param field Field name
   * @param value Field value
   */
  like(field: string, value: string): FilterBuilder {
    return this.add(new LikeFilter(field, value));
  }

  /**
   * Add an ilike filter
   * @param field Field name
   * @param value Field value
   */
  ilike(field: string, value: string): FilterBuilder {
    return this.add(new ILikeFilter(field, value));
  }

  /**
   * Add a starts with filter
   * @param field Field name
   * @param value Field value
   */
  startsWith(field: string, value: string): FilterBuilder {
    return this.add(new StartsWithFilter(field, value));
  }

  /**
   * Add an ends with filter
   * @param field Field name
   * @param value Field value
   */
  endsWith(field: string, value: string): FilterBuilder {
    return this.add(new EndsWithFilter(field, value));
  }

  /**
   * Add an is null filter
   * @param field Field name
   */
  isNull(field: string): FilterBuilder {
    return this.add(new IsNullFilter(field));
  }

  /**
   * Add an is not null filter
   * @param field Field name
   */
  isNotNull(field: string): FilterBuilder {
    return this.add(new IsNotNullFilter(field));
  }

  /**
   * Build the filter
   * @returns Composite filter with all added filters
   */
  build(): IFilter {
    if (this.filters.length === 0) {
      return new CompositeFilter([], 'AND');
    }

    if (this.filters.length === 1) {
      return this.filters[0];
    }

    return new CompositeFilter(this.filters, 'AND');
  }
}
