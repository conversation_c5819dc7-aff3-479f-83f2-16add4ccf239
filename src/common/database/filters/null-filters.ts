import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import { Filter } from './filter';

/**
 * Is Null Filter
 * Filters records where field is null
 */
export class IsNullFilter extends Filter {
  private field: string;

  /**
   * Constructor
   * @param field Field name
   */
  constructor(field: string) {
    super();
    this.field = field;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    return queryBuilder.andWhere(`${fieldName} IS NULL`);
  }
}

/**
 * Is Not Null Filter
 * Filters records where field is not null
 */
export class IsNotNullFilter extends Filter {
  private field: string;

  /**
   * Constructor
   * @param field Field name
   */
  constructor(field: string) {
    super();
    this.field = field;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    return queryBuilder.andWhere(`${fieldName} IS NOT NULL`);
  }
}
