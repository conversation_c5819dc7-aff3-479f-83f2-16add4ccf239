import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import { Filter } from './filter';

/**
 * Like Filter
 * Filters records where field contains value (case sensitive)
 */
export class LikeFilter extends Filter {
  private field: string;
  private value: string;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: string) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if (!this.value) {
      return queryBuilder;
    }

    const paramName = `${this.field}_like`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    return queryBuilder.andWhere(`${fieldName} LIKE :${paramName}`, {
      [paramName]: `%${this.value}%`,
    });
  }
}

/**
 * ILike Filter
 * Filters records where field contains value (case insensitive)
 */
export class ILikeFilter extends Filter {
  private field: string;
  private value: string;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: string) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if (!this.value) {
      return queryBuilder;
    }

    const paramName = `${this.field}_ilike`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    return queryBuilder.andWhere(`${fieldName} ILIKE :${paramName}`, {
      [paramName]: `%${this.value}%`,
    });
  }
}

/**
 * Starts With Filter
 * Filters records where field starts with value (case sensitive)
 */
export class StartsWithFilter extends Filter {
  private field: string;
  private value: string;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: string) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if (!this.value) {
      return queryBuilder;
    }

    const paramName = `${this.field}_startsWith`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    return queryBuilder.andWhere(`${fieldName} LIKE :${paramName}`, {
      [paramName]: `${this.value}%`,
    });
  }
}

/**
 * Ends With Filter
 * Filters records where field ends with value (case sensitive)
 */
export class EndsWithFilter extends Filter {
  private field: string;
  private value: string;

  /**
   * Constructor
   * @param field Field name
   * @param value Field value
   */
  constructor(field: string, value: string) {
    super();
    this.field = field;
    this.value = value;
  }

  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T> {
    if (!this.value) {
      return queryBuilder;
    }

    const paramName = `${this.field}_endsWith`;
    const fieldName = alias ? `${alias}.${this.field}` : this.field;
    
    return queryBuilder.andWhere(`${fieldName} LIKE :${paramName}`, {
      [paramName]: `%${this.value}`,
    });
  }
}
