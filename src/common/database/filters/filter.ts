import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';

/**
 * Base Filter interface
 * All filter types must implement this interface
 */
export interface IFilter {
  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T>;
}

/**
 * Base Filter class
 * Provides common functionality for all filter types
 */
export abstract class Filter implements IFilter {
  /**
   * Apply the filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  abstract apply<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T>;
}

/**
 * Composite Filter class
 * Used to combine multiple filters with AND or OR operators
 */
export class CompositeFilter extends Filter {
  private filters: IFilter[];
  private operator: 'AND' | 'OR';

  /**
   * Constructor
   * @param filters Array of filters to combine
   * @param operator Operator to use for combining filters (AND or OR)
   */
  constructor(filters: IFilter[], operator: 'AND' | 'OR' = 'AND') {
    super();
    this.filters = filters;
    this.operator = operator;
  }

  /**
   * Apply the composite filter to a query builder
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  apply<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T> {
    if (this.filters.length === 0) {
      return queryBuilder;
    }

    if (this.operator === 'AND') {
      return this.applyAnd(queryBuilder, alias);
    } else {
      return this.applyOr(queryBuilder, alias);
    }
  }

  /**
   * Apply filters with AND operator
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  private applyAnd<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T> {
    this.filters.forEach((filter) => {
      filter.apply(queryBuilder, alias);
    });
    return queryBuilder;
  }

  /**
   * Apply filters with OR operator
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  private applyOr<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    alias?: string,
  ): SelectQueryBuilder<T> {
    // For OR operator, we need to use brackets
    return queryBuilder.andWhere(
      new Brackets((qb) => {
        this.filters.forEach((filter, index) => {
          if (index === 0) {
            // Create a new query builder for the first filter
            const subQuery = new SelectQueryBuilder<
              T extends ObjectLiteral ? T : never
            >(queryBuilder.connection);
            filter.apply(subQuery, alias);
            // Extract the where clause from the subQuery
            const whereClause = subQuery
              .getQuery()
              .replace(/^SELECT.*FROM.*WHERE/i, '');
            qb.where(whereClause, subQuery.getParameters());
          } else {
            // Create a new query builder for subsequent filters
            const subQuery = new SelectQueryBuilder<
              T extends ObjectLiteral ? T : never
            >(queryBuilder.connection);
            filter.apply(subQuery, alias);
            // Extract the where clause from the subQuery
            const whereClause = subQuery
              .getQuery()
              .replace(/^SELECT.*FROM.*WHERE/i, '');
            qb.orWhere(whereClause, subQuery.getParameters());
          }
        });
      }),
    );
  }
}

/**
 * Helper class for query builder brackets
 */
class Brackets {
  constructor(private readonly buildCallback: (qb: any) => void) {}

  /**
   * Apply the brackets to a query builder
   * @param qb Query builder
   */
  public build(qb: any): void {
    this.buildCallback(qb);
  }
}
