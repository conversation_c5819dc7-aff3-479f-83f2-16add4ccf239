import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import { PaginationDto } from '@common/dto/pagination.dto';
import { IFilter } from './filters';

/**
 * QueryBuilderHelper class
 * This class provides methods to build complex queries with TypeORM QueryBuilder
 * It follows the builder pattern for method chaining
 */
export class QueryBuilderHelper<T extends ObjectLiteral> {
  private queryBuilder: SelectQueryBuilder<T>;
  private alias: string;

  /**
   * Constructor
   * @param queryBuilder TypeORM query builder
   * @param alias Table alias
   */
  constructor(queryBuilder: SelectQueryBuilder<T>, alias: string = 'entity') {
    this.queryBuilder = queryBuilder;
    this.alias = alias;
  }

  /**
   * Apply pagination to the query
   * @param paginationDto Pagination DTO
   * @returns QueryBuilderHelper instance for method chaining
   */
  applyPagination(paginationDto: PaginationDto): QueryBuilderHelper<T> {
    const { skip, limit } = paginationDto;
    this.queryBuilder.skip(skip).take(limit);
    return this;
  }

  /**
   * Apply sorting to the query
   * @param sortBy Field to sort by
   * @param sortOrder Sort order (ASC or DESC)
   * @returns QueryBuilderHelper instance for method chaining
   */
  applySorting(
    sortBy: string,
    sortOrder: 'ASC' | 'DESC',
  ): QueryBuilderHelper<T> {
    this.queryBuilder.orderBy(`${this.alias}.${sortBy}`, sortOrder);
    return this;
  }

  /**
   * Apply a filter to the query
   * @param filter Filter to apply
   * @returns QueryBuilderHelper instance for method chaining
   */
  applyFilter(filter: IFilter): QueryBuilderHelper<T> {
    if (filter) {
      filter.apply(this.queryBuilder, this.alias);
    }
    return this;
  }

  /**
   * Apply multiple filters to the query
   * @param filters Array of filters to apply
   * @returns QueryBuilderHelper instance for method chaining
   */
  applyFilters(filters: IFilter[]): QueryBuilderHelper<T> {
    if (filters && filters.length > 0) {
      filters.forEach((filter) => {
        this.applyFilter(filter);
      });
    }
    return this;
  }

  /**
   * Apply exact match filter to the query
   * @param field Field name
   * @param value Field value
   * @param customAlias Optional custom alias
   * @returns QueryBuilderHelper instance for method chaining
   */
  applyExactMatchFilter(
    field: string,
    value: any,
    customAlias?: string,
  ): QueryBuilderHelper<T> {
    if (value === undefined || value === null || value === '') {
      return this;
    }

    const alias = customAlias || this.alias;
    this.queryBuilder.andWhere(`${alias}.${field} = :${field}`, {
      [field]: value,
    });
    return this;
  }

  /**
   * Apply partial match filter to the query (ILIKE)
   * @param field Field name
   * @param value Field value
   * @param customAlias Optional custom alias
   * @returns QueryBuilderHelper instance for method chaining
   */
  applyPartialMatchFilter(
    field: string,
    value: string,
    customAlias?: string,
  ): QueryBuilderHelper<T> {
    if (!value) {
      return this;
    }

    const alias = customAlias || this.alias;
    this.queryBuilder.andWhere(`${alias}.${field} ILIKE :${field}`, {
      [field]: `%${value}%`,
    });
    return this;
  }

  /**
   * Apply date range filter to the query
   * @param field Field name
   * @param startDate Start date
   * @param endDate End date
   * @param customAlias Optional custom alias
   * @returns QueryBuilderHelper instance for method chaining
   */
  applyDateRangeFilter(
    field: string,
    startDate?: Date,
    endDate?: Date,
    customAlias?: string,
  ): QueryBuilderHelper<T> {
    if (!startDate && !endDate) {
      return this;
    }

    const alias = customAlias || this.alias;

    if (startDate) {
      this.queryBuilder.andWhere(`${alias}.${field} >= :${field}Start`, {
        [`${field}Start`]: startDate,
      });
    }

    if (endDate) {
      this.queryBuilder.andWhere(`${alias}.${field} <= :${field}End`, {
        [`${field}End`]: endDate,
      });
    }

    return this;
  }

  /**
   * Apply number range filter to the query
   * @param field Field name
   * @param min Minimum value
   * @param max Maximum value
   * @param customAlias Optional custom alias
   * @returns QueryBuilderHelper instance for method chaining
   */
  applyNumberRangeFilter(
    field: string,
    min?: number,
    max?: number,
    customAlias?: string,
  ): QueryBuilderHelper<T> {
    if (min === undefined && max === undefined) {
      return this;
    }

    const alias = customAlias || this.alias;

    if (min !== undefined) {
      this.queryBuilder.andWhere(`${alias}.${field} >= :${field}Min`, {
        [`${field}Min`]: min,
      });
    }

    if (max !== undefined) {
      this.queryBuilder.andWhere(`${alias}.${field} <= :${field}Max`, {
        [`${field}Max`]: max,
      });
    }

    return this;
  }

  /**
   * Add a join to the query
   * @param relation Relation to join
   * @param alias Alias for the joined table
   * @param condition Optional join condition
   * @returns QueryBuilderHelper instance for method chaining
   */
  addInnerJoin(
    relation: string,
    alias: string,
    condition?: string,
  ): QueryBuilderHelper<T> {
    this.queryBuilder.innerJoin(`${this.alias}.${relation}`, alias, condition);
    return this;
  }

  /**
   * Add a left join to the query
   * @param relation Relation to join
   * @param alias Alias for the joined table
   * @param condition Optional join condition
   * @returns QueryBuilderHelper instance for method chaining
   */
  addLeftJoin(
    relation: string,
    alias: string,
    condition?: string,
  ): QueryBuilderHelper<T> {
    this.queryBuilder.leftJoin(`${this.alias}.${relation}`, alias, condition);
    return this;
  }

  /**
   * Execute the query and get results with count
   * @returns Promise with results and count
   */
  async getResultsAndCount(): Promise<[T[], number]> {
    return this.queryBuilder.getManyAndCount();
  }

  /**
   * Execute the query and get results
   * @returns Promise with results
   */
  async getResults(): Promise<T[]> {
    return this.queryBuilder.getMany();
  }

  /**
   * Get the query builder instance
   * @returns TypeORM query builder
   */
  getQueryBuilder(): SelectQueryBuilder<T> {
    return this.queryBuilder;
  }
}
