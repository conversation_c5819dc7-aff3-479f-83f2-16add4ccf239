import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from '@core/database/database.module';
import { RedisModule } from '@core/redis/redis.module';
import { FirebaseModule } from '@core/firebase/firebase.module';
import { CommonModule } from '@common/common.module';
import { UsersModule } from '@modules/users/users.module';
import { AuthModule } from '@modules/auth/auth.module';
import { ProductsModule } from '@modules/products/products.module';
import { AuthorizationModule } from '@modules/authorization/authorization.module';
import configs from '@config/index';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: configs,
      envFilePath: '.env',
    }),
    DatabaseModule,
    RedisModule,
    FirebaseModule,
    CommonModule,
    UsersModule,
    AuthModule,
    AuthorizationModule,
    ProductsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
