import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpAdapterHost } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { AllExceptionsFilter } from '@common/filters/all-exceptions.filter';
import { TransformInterceptor } from '@common/interceptors/transform.interceptor';
import {
  DEFAULT_API_PREFIX,
  DEFAULT_API_VERSION,
  DEFAULT_PORT,
} from '@shared/constants/app.constant';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const httpAdapterHost = app.get(HttpAdapterHost);
  const logger = new Logger('Bootstrap');

  // Enable validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Global exception filter
  app.useGlobalFilters(new AllExceptionsFilter(httpAdapterHost));

  // Global transform interceptor
  app.useGlobalInterceptors(new TransformInterceptor());

  // Enable CORS
  app.enableCors();

  // Set global prefix
  const apiPrefix = configService.get<string>(
    'app.apiPrefix',
    DEFAULT_API_PREFIX,
  );
  const apiVersion = configService.get<string>(
    'app.apiVersion',
    DEFAULT_API_VERSION,
  );
  app.setGlobalPrefix(`${apiPrefix}/${apiVersion}`);

  // Setup Swagger
  const swaggerConfig = new DocumentBuilder()
    .setTitle('OneCommerce API')
    .setDescription('The OneCommerce API documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup(`${apiPrefix}/${apiVersion}/docs`, app, document);

  // Start the server
  const port = configService.get<number>('app.port', DEFAULT_PORT);
  // Use a different port if 3000 is already in use
  const actualPort = process.env.PORT ? parseInt(process.env.PORT, 10) : port;
  console.log('🔥 ~ bootstrap ~ port:', port);
  await app.listen(actualPort);

  logger.log(
    `Application is running on: http://localhost:${actualPort}/${apiPrefix}/${apiVersion}`,
  );
  logger.log(
    `Swagger documentation is available at: http://localhost:${actualPort}/${apiPrefix}/${apiVersion}/docs`,
  );
}
bootstrap();
