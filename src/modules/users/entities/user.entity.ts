import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '@core/database/base/base.entity';
import { UserRole } from '@modules/authorization/entities/user-role.entity';
import { UserTypeEnum } from '../user.constant';

/**
 * User entity
 * This entity represents a user in the system
 */
@Entity('users')
export class User extends BaseEntity {
  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({ type: 'enum', enum: UserTypeEnum, default: UserTypeEnum.Local })
  type: UserTypeEnum;

  @Column()
  name: string;

  @Column({ select: false, nullable: true })
  password: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  avatar: string;

  @Column({ default: false })
  isSuperAdmin: boolean;

  @Column({ nullable: true, unique: true })
  firebaseUid: string;

  @OneToMany(() => UserRole, (userRole) => userRole.user)
  userRoles: UserRole[];
}
