import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsO<PERSON>al,
  IsString,
  MinLeng<PERSON>,
} from 'class-validator';
import { UserTypeEnum } from '../user.constant';

export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  @MinLength(6)
  password?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsString()
  @IsOptional()
  avatar?: string;

  @IsBoolean()
  @IsOptional()
  isSuperAdmin?: boolean;

  @IsString()
  @IsOptional()
  firebaseUid?: string;

  @IsEnum(UserTypeEnum)
  @IsOptional()
  type?: UserTypeEnum;
}
