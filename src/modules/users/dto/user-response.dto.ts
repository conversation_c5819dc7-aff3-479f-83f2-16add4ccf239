import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { User } from '@modules/users/entities/user.entity';

@Exclude()
export class UserResponseDto {
  @Expose()
  @ApiProperty({
    description: 'User ID',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @Expose()
  @ApiProperty({
    description: 'User name',
    example: '<PERSON>',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'User active status',
    example: true,
  })
  isActive: boolean;

  @Expose()
  @ApiPropertyOptional({
    description: 'User Google ID',
    example: '123456789012345678901',
  })
  googleId?: string;

  @Expose()
  @ApiPropertyOptional({
    description: 'User avatar URL',
    example: 'https://lh3.googleusercontent.com/a/photo.jpg',
  })
  avatar?: string;

  @Expose()
  @ApiPropertyOptional({
    description: 'Whether the user is a super admin',
    example: false,
  })
  isSuperAdmin?: boolean;

  @Expose()
  @ApiProperty({
    description: 'User creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @Expose()
  @ApiProperty({
    description: 'User last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  constructor(user: User) {
    Object.assign(this, user);
  }
}
