import {
  IsBoolean,
  IsEmail,
  IsO<PERSON>al,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class UpdateUserDto {
  @IsEmail()
  @IsOptional()
  email?: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  @MinLength(6)
  password?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsString()
  @IsOptional()
  avatar?: string;

  @IsBoolean()
  @IsOptional()
  isSuperAdmin?: boolean;

  @IsString()
  @IsOptional()
  firebaseUid?: string;
}
