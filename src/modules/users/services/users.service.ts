import { Inject, Injectable } from '@nestjs/common';
import { CreateUserDto } from '@modules/users/dto/create-user.dto';
import { UpdateUserDto } from '@modules/users/dto/update-user.dto';
import { FindUsersDto } from '@modules/users/dto/find-users.dto';
import {
  IUserRepository,
  USER_REPOSITORY,
} from '@modules/users/repositories/user.repository.interface';
import { User } from '@modules/users/entities/user.entity';
import { PageMetaDto } from '@common/dto/page-meta.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { UserResponseDto } from '@modules/users/dto/user-response.dto';
import { UserTypeEnum } from '../user.constant';

@Injectable()
export class UsersService {
  constructor(
    @Inject(USER_REPOSITORY)
    private readonly userRepository: IUserRepository,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    return this.userRepository.create(createUserDto);
  }

  async findAll(): Promise<User[]> {
    return this.userRepository.findAll();
  }

  /**
   * Find users with pagination, filtering and sorting
   * @param findUsersDto DTO with pagination, filter and sort options
   * @returns Promise with paginated users
   */
  async findUsers(
    findUsersDto: FindUsersDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    // Get users with pagination, filtering and sorting
    const [users, totalItems] =
      await this.userRepository.findUsers(findUsersDto);

    // Create page metadata
    const pageMetaDto = new PageMetaDto({
      paginationDto: findUsersDto,
      totalItems,
    });

    // Map users to response DTOs
    const userResponseDtos = users.map((user) => new UserResponseDto(user));

    // Return paginated response
    return new ApiResponseDto(
      userResponseDtos,
      pageMetaDto,
      'Users retrieved successfully',
    );
  }

  async findOne(id: number): Promise<User | null> {
    return this.userRepository.findOneById(id);
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findByEmail(email);
  }

  async findByFirebaseUid(firebaseUid: string): Promise<User | null> {
    return this.userRepository.findByFirebaseUid(firebaseUid);
  }

  async findOrCreateFirebaseUser(firebaseUser: {
    uid: string;
    email: string;
    name: string;
    picture?: string;
  }): Promise<User> {
    // Try to find user by Firebase UID
    let user = await this.findByFirebaseUid(firebaseUser.uid);

    // If user doesn't exist, try to find by email
    if (!user) {
      user = await this.findByEmail(firebaseUser.email);

      // If user exists with this email, update with Firebase info
      if (user) {
        user = await this.update(user.id, {
          firebaseUid: firebaseUser.uid,
          avatar: firebaseUser.picture || user.avatar,
        });
      } else {
        // Create new user
        user = await this.create({
          email: firebaseUser.email,
          name: firebaseUser.name,
          firebaseUid: firebaseUser.uid,
          avatar: firebaseUser.picture,
          isActive: true,
          type: UserTypeEnum.Google,
        });
      }
    }

    return user;
  }

  async update(id: number, updateUserDto: UpdateUserDto): Promise<User> {
    return this.userRepository.update(id, updateUserDto);
  }

  async remove(id: number): Promise<void> {
    return this.userRepository.remove(id);
  }
}
