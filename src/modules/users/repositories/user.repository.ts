import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BaseRepository } from '@core/database/base/base.repository';
import { User } from '@modules/users/entities/user.entity';
import { IUserRepository } from '@modules/users/repositories/user.repository.interface';
import { DATABASE_CONNECTION } from '@core/database/database.providers';
import { FindUsersDto } from '@modules/users/dto/find-users.dto';
import { QueryBuilderService } from '@common/services/query-builder.service';

/**
 * PostgreSQL implementation of the user repository
 * This class extends the base repository and implements the user repository interface
 *
 * When switching to MongoDB, you would create a new implementation of the IUserRepository
 * interface that uses MongoDB, but keep the same interface
 */
@Injectable()
export class UserRepository
  extends BaseRepository<User>
  implements IUserRepository
{
  private readonly userRepository: Repository<User>;

  constructor(
    @Inject(DATABASE_CONNECTION)
    dataSource: DataSource,
    private queryBuilderService: QueryBuilderService,
  ) {
    const userRepository = dataSource.getRepository(User);
    super(userRepository);
    this.userRepository = userRepository;
  }

  /**
   * Find a user by email
   * @param email User email
   * @returns Promise with the found user or null
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } });
  }

  /**
   * Find a user by Firebase UID
   * @param firebaseUid Firebase UID
   * @returns Promise with the found user or null
   */
  async findByFirebaseUid(firebaseUid: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { firebaseUid } });
  }

  /**
   * Find users with pagination, filtering and sorting
   * @param findUsersDto DTO with pagination, filter and sort options
   * @returns Promise with users and count
   */
  async findUsers(findUsersDto: FindUsersDto): Promise<[User[], number]> {
    // Create query builder
    const queryBuilder = this.userRepository.createQueryBuilder('user');

    // Xử lý filter, sort và pagination sử dụng QueryBuilderService
    // để đảm bảo tính bảo mật

    // 1. Xử lý filter
    // Filter theo email
    if (findUsersDto.email) {
      this.queryBuilderService.applyPartialMatchFilter(
        queryBuilder,
        'email',
        findUsersDto.email,
        'user',
      );
    }

    // Filter theo name
    if (findUsersDto.name) {
      this.queryBuilderService.applyPartialMatchFilter(
        queryBuilder,
        'name',
        findUsersDto.name,
        'user',
      );
    }

    // Filter theo isActive
    if (findUsersDto.isActive !== undefined) {
      this.queryBuilderService.applyExactMatchFilter(
        queryBuilder,
        'isActive',
        findUsersDto.isActive,
        'user',
      );
    }

    // 2. Xử lý sorting
    const sortBy = findUsersDto.sortBy || 'id';
    const sortOrder = findUsersDto.sortOrder || 'ASC';
    this.queryBuilderService.applySorting(
      queryBuilder,
      sortBy,
      sortOrder,
      'user',
    );

    // 3. Xử lý pagination
    this.queryBuilderService.applyPagination(queryBuilder, findUsersDto);

    // Execute query and return results with count
    return queryBuilder.getManyAndCount();
  }
}
