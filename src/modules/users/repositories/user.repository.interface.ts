import { IBaseRepository } from '@core/database/base/base.repository.interface';
import { User } from '@modules/users/entities/user.entity';
import { FindUsersDto } from '@modules/users/dto/find-users.dto';

/**
 * User repository interface
 * This interface extends the base repository interface and adds user-specific methods
 * When switching to MongoDB, you would implement this interface with MongoDB-specific code
 */
export interface IUserRepository extends IBaseRepository<User> {
  /**
   * Find a user by email
   * @param email User email
   * @returns Promise with the found user or null
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * Find a user by Firebase UID
   * @param firebaseUid Firebase UID
   * @returns Promise with the found user or null
   */
  findByFirebaseUid(firebaseUid: string): Promise<User | null>;

  /**
   * Find users with pagination, filtering and sorting
   * @param findUsersDto DTO with pagination, filter and sort options
   * @returns Promise with users and count
   */
  findUsers(findUsersDto: FindUsersDto): Promise<[User[], number]>;
}

/**
 * User repository token for dependency injection
 */
export const USER_REPOSITORY = 'USER_REPOSITORY';
