import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, Repository } from 'typeorm';
import { UserRepository } from '@modules/users/repositories/user.repository';
import { User } from '@modules/users/entities/user.entity';
import { DATABASE_CONNECTION } from '@core/database/database.providers';

describe('UserRepository', () => {
  let userRepository: UserRepository;
  let mockRepository: Partial<Repository<User>>;
  let mockDataSource: Partial<DataSource>;

  beforeEach(async () => {
    // Create mock repository methods
    mockRepository = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      remove: jest.fn(),
    };

    // Create mock DataSource
    mockDataSource = {
      getRepository: jest.fn().mockReturnValue(mockRepository),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRepository,
        {
          provide: DATABASE_CONNECTION,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(userRepository).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        name: 'Test User',
        password: 'password123',
      };

      const createdUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        password: 'password123',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (mockRepository.create as jest.Mock).mockReturnValue(createdUser);
      (mockRepository.save as jest.Mock).mockResolvedValue(createdUser);

      const result = await userRepository.create(userData);

      expect(mockRepository.create).toHaveBeenCalledWith(userData);
      expect(mockRepository.save).toHaveBeenCalledWith(createdUser);
      expect(result).toEqual(createdUser);
    });
  });

  describe('findByEmail', () => {
    it('should find a user by email', async () => {
      const email = '<EMAIL>';
      const user = {
        id: '1',
        email,
        name: 'Test User',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (mockRepository.findOne as jest.Mock).mockResolvedValue(user);

      const result = await userRepository.findByEmail(email);

      expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { email } });
      expect(result).toEqual(user);
    });

    it('should return null if user not found', async () => {
      const email = '<EMAIL>';

      (mockRepository.findOne as jest.Mock).mockResolvedValue(null);

      const result = await userRepository.findByEmail(email);

      expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { email } });
      expect(result).toBeNull();
    });
  });
});
