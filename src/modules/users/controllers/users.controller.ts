import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ParseIdPipe } from '@common/pipes/parse-id.pipe';
import { UsersService } from '@modules/users/services/users.service';
import { CreateUserDto } from '@modules/users/dto/create-user.dto';
import { UpdateUserDto } from '@modules/users/dto/update-user.dto';
import { FindUsersDto } from '@modules/users/dto/find-users.dto';
import { UserResponseDto } from '@modules/users/dto/user-response.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { RequirePermission } from '@modules/authorization/decorators/require-permission.decorator';
import { USER_PERMISSIONS } from '@modules/authorization/constants';

@ApiTags('users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @RequirePermission(USER_PERMISSIONS.CREATE)
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({
    status: 201,
    description: 'The user has been successfully created.',
    type: UserResponseDto,
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    const user = await this.usersService.create(createUserDto);
    return new UserResponseDto(user);
  }

  @Get()
  @RequirePermission(USER_PERMISSIONS.READ)
  @ApiOperation({
    summary: 'Get all users with pagination, filtering and sorting',
  })
  @ApiResponse({
    status: 200,
    description: 'Return users with pagination metadata',
    type: ApiResponseDto<UserResponseDto>,
  })
  async findUsers(
    @Query() findUsersDto: FindUsersDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    return this.usersService.findUsers(findUsersDto);
  }

  @Get(':id')
  @RequirePermission(USER_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the user with the specified ID',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async findOne(
    @Param('id', ParseIdPipe) id: number,
  ): Promise<UserResponseDto> {
    const user = await this.usersService.findOne(id);
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return new UserResponseDto(user);
  }

  @Patch(':id')
  @RequirePermission(USER_PERMISSIONS.UPDATE)
  @ApiOperation({ summary: 'Update a user' })
  @ApiResponse({
    status: 200,
    description: 'The user has been successfully updated.',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async update(
    @Param('id', ParseIdPipe) id: number,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    const user = await this.usersService.update(id, updateUserDto);
    return new UserResponseDto(user);
  }

  @Delete(':id')
  @RequirePermission(USER_PERMISSIONS.DELETE)
  @ApiOperation({ summary: 'Delete a user' })
  @ApiResponse({
    status: 200,
    description: 'The user has been successfully deleted.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async remove(
    @Param('id', ParseIdPipe) id: number,
  ): Promise<{ success: boolean }> {
    await this.usersService.remove(id);
    return { success: true };
  }
}
