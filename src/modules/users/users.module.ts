import { Module } from '@nestjs/common';
import { UsersService } from '@modules/users/services/users.service';
import { UsersController } from '@modules/users/controllers/users.controller';
import { DatabaseModule } from '@core/database/database.module';
import { CommonModule } from '@common/common.module';
import { UserRepository } from '@modules/users/repositories/user.repository';
import { USER_REPOSITORY } from '@modules/users/repositories/user.repository.interface';

/**
 * Users module
 * This module provides user-related functionality
 *
 * The repository is injected using a token, which makes it easy to switch
 * to a different implementation (e.g., MongoDB) in the future
 */
@Module({
  imports: [DatabaseModule, CommonModule],
  controllers: [UsersController],
  providers: [
    UsersService,
    {
      provide: USER_REPOSITORY,
      useClass: UserRepository, // To switch to MongoDB, change this to MongoUserRepository
    },
  ],
  exports: [UsersService],
})
export class UsersModule {}
