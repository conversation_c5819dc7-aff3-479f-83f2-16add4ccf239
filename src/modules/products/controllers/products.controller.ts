import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ParseIdPipe } from '@common/pipes/parse-id.pipe';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { ProductsService } from '@modules/products/services/products.service';
import { CreateProductDto } from '@modules/products/dto/create-product.dto';
import { UpdateProductDto } from '@modules/products/dto/update-product.dto';
import { FindProductsDto } from '@modules/products/dto/find-products.dto';
import { AdvancedFindProductsDto } from '@modules/products/dto/advanced-find-products.dto';
import { ProductResponseDto } from '@modules/products/dto/product-response.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { Public } from '@common/decorators/public.decorator';
import { RequirePermission } from '@modules/authorization/decorators/require-permission.decorator';
import { PRODUCT_PERMISSIONS } from '@modules/authorization/constants';

@ApiTags('products')
@Controller('products')
@UseGuards(JwtAuthGuard)
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermission(PRODUCT_PERMISSIONS.CREATE)
  @ApiOperation({ summary: 'Create a new product' })
  @ApiResponse({
    status: 201,
    description: 'The product has been successfully created.',
  })
  create(@Body() createProductDto: CreateProductDto) {
    return this.productsService.create(createProductDto);
  }

  @Get('search')
  @Public()
  @ApiOperation({
    summary: 'Search products with filtering, sorting and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Return products with pagination metadata',
    type: ApiResponseDto<ProductResponseDto>,
  })
  async findProducts(
    @Query() findProductsDto: FindProductsDto,
  ): Promise<ApiResponseDto<ProductResponseDto>> {
    return this.productsService.findProducts(findProductsDto);
  }

  @Get('advanced-search')
  @Public()
  @ApiOperation({
    summary: 'Search products with advanced filtering, sorting and pagination',
  })
  @ApiResponse({
    status: 200,
    description:
      'Return products with pagination metadata using advanced filtering',
    type: ApiResponseDto<ProductResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
  })
  async findProductsAdvanced(
    @Query() advancedFindProductsDto: AdvancedFindProductsDto,
  ): Promise<ApiResponseDto<ProductResponseDto | any>> {
    return this.productsService.findProductsAdvanced(advancedFindProductsDto);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all products' })
  @ApiResponse({
    status: 200,
    description: 'Return all products',
  })
  findAll() {
    return this.productsService.findAll();
  }

  @Get('active')
  @Public()
  @ApiOperation({ summary: 'Get all active products' })
  @ApiResponse({
    status: 200,
    description: 'Return all active products',
  })
  findActive() {
    return this.productsService.findActive();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get a product by id' })
  @ApiResponse({
    status: 200,
    description: 'Return the product',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  findOne(@Param('id', ParseIdPipe) id: number) {
    return this.productsService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermission(PRODUCT_PERMISSIONS.UPDATE)
  @ApiOperation({ summary: 'Update a product' })
  @ApiResponse({
    status: 200,
    description: 'The product has been successfully updated.',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  update(
    @Param('id', ParseIdPipe) id: number,
    @Body() updateProductDto: UpdateProductDto,
  ) {
    return this.productsService.update(id, updateProductDto);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermission(PRODUCT_PERMISSIONS.DELETE)
  @ApiOperation({ summary: 'Delete a product' })
  @ApiResponse({
    status: 200,
    description: 'The product has been successfully deleted.',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  remove(@Param('id', ParseIdPipe) id: number) {
    return this.productsService.remove(id);
  }
}
