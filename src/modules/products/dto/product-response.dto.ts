import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { Product } from '@modules/products/entities/product.entity';

@Exclude()
export class ProductResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Product ID',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Product name',
    example: 'Laptop XPS 13',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Product description',
    example: 'High performance laptop with 16GB RAM',
  })
  description: string;

  @Expose()
  @ApiProperty({
    description: 'Product price',
    example: 1299.99,
  })
  price: number;

  @Expose()
  @ApiProperty({
    description: 'Product image URL',
    example: 'https://example.com/images/laptop.jpg',
  })
  imageUrl: string;

  @Expose()
  @ApiProperty({
    description: 'Product active status',
    example: true,
  })
  isActive: boolean;

  @Expose()
  @ApiProperty({
    description: 'Product creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @Expose()
  @ApiProperty({
    description: 'Product last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  constructor(product: Product) {
    Object.assign(this, product);
  }
}
