import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { AdvancedFilterDto } from '@common/dto/advanced-filter.dto';

export enum ProductSortField {
  ID = 'id',
  NAME = 'name',
  PRICE = 'price',
  CREATED_AT = 'createdAt',
}

export class AdvancedFindProductsDto extends AdvancedFilterDto {
  @ApiPropertyOptional({
    description: 'Default field to sort by if no sort criteria provided',
    enum: ProductSortField,
    default: ProductSortField.ID,
  })
  @IsEnum(ProductSortField)
  @IsOptional()
  defaultSortField?: ProductSortField = ProductSortField.ID;
}
