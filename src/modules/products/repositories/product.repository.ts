import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BaseRepository } from '@core/database/base/base.repository';
import { Product } from '@modules/products/entities/product.entity';
import { IProductRepository } from '@modules/products/repositories/product.repository.interface';
import { DATABASE_CONNECTION } from '@core/database/database.providers';
import { FindProductsDto } from '@modules/products/dto/find-products.dto';
import { AdvancedFindProductsDto } from '@modules/products/dto/advanced-find-products.dto';
import { SortOrder } from '@common/dto/advanced-filter.dto';
import { QueryBuilderService } from '@common/services/query-builder.service';
import { AdvancedFilterService } from '@common/services/advanced-filter.service';

/**
 * PostgreSQL implementation of the product repository
 * This class extends the base repository and implements the product repository interface
 *
 * When switching to MongoDB, you would create a new implementation of the IProductRepository
 * interface that uses MongoDB, but keep the same interface
 */
@Injectable()
export class ProductRepository
  extends BaseRepository<Product>
  implements IProductRepository
{
  private readonly productRepository: Repository<Product>;

  constructor(
    @Inject(DATABASE_CONNECTION)
    dataSource: DataSource,
    private queryBuilderService: QueryBuilderService,
    private advancedFilterService: AdvancedFilterService,
  ) {
    const productRepository = dataSource.getRepository(Product);
    super(productRepository);
    this.productRepository = productRepository;
  }

  /**
   * Find a product by name
   * @param name Product name
   * @returns Promise with the found product or null
   */
  async findByName(name: string): Promise<Product | null> {
    return this.productRepository.findOne({ where: { name } });
  }

  /**
   * Find all active products
   * @returns Promise with array of active products
   */
  async findActiveProducts(): Promise<Product[]> {
    return this.productRepository.find({ where: { isActive: true } });
  }

  /**
   * Find products with advanced filtering, sorting and pagination using QueryBuilder
   * @param findProductsDto DTO with filter, sort and pagination options
   * @returns Promise with products and count
   */
  async findProducts(
    findProductsDto: FindProductsDto,
  ): Promise<[Product[], number]> {
    // Create query builder
    const queryBuilder = this.productRepository.createQueryBuilder('product');

    // Xử lý filter, sort và pagination sử dụng QueryBuilderService
    // để đảm bảo tính bảo mật

    // 1. Xử lý join
    if (findProductsDto.categoryId) {
      queryBuilder.innerJoin('product.category', 'category');
    }

    // 2. Xử lý filter
    // Filter theo name
    if (findProductsDto.name) {
      this.queryBuilderService.applyPartialMatchFilter(
        queryBuilder,
        'name',
        findProductsDto.name,
        'product',
      );
    }

    // Filter theo description
    if (findProductsDto.description) {
      this.queryBuilderService.applyPartialMatchFilter(
        queryBuilder,
        'description',
        findProductsDto.description,
        'product',
      );
    }

    // Filter theo category
    if (findProductsDto.categoryId) {
      this.queryBuilderService.applyExactMatchFilter(
        queryBuilder,
        'id',
        findProductsDto.categoryId,
        'category',
      );
    }

    // Filter theo price range
    if (findProductsDto.minPrice !== undefined) {
      this.queryBuilderService.applyExactMatchFilter(
        queryBuilder,
        'price',
        findProductsDto.minPrice,
        'product',
        '>=',
      );
    }

    if (findProductsDto.maxPrice !== undefined) {
      this.queryBuilderService.applyExactMatchFilter(
        queryBuilder,
        'price',
        findProductsDto.maxPrice,
        'product',
        '<=',
      );
    }

    // Filter theo date range
    if (findProductsDto.createdAfter || findProductsDto.createdBefore) {
      const startDate = findProductsDto.createdAfter
        ? new Date(findProductsDto.createdAfter)
        : undefined;
      const endDate = findProductsDto.createdBefore
        ? new Date(findProductsDto.createdBefore)
        : undefined;

      this.queryBuilderService.applyDateRangeFilter(
        queryBuilder,
        'createdAt',
        startDate,
        endDate,
        'product',
      );
    }

    // 3. Xử lý sorting
    const sortBy = findProductsDto.sortBy || 'id';
    const sortOrder = findProductsDto.sortOrder || 'ASC';
    this.queryBuilderService.applySorting(
      queryBuilder,
      sortBy,
      sortOrder,
      'product',
    );

    // 4. Xử lý pagination
    this.queryBuilderService.applyPagination(queryBuilder, findProductsDto);

    // Execute query and return results with count
    return queryBuilder.getManyAndCount();
  }

  /**
   * Find products with complex filtering, sorting and pagination
   * @param advancedFindProductsDto Advanced DTO with complex filter, sort and pagination options
   * @returns Promise with products and count
   */
  async findProductsAdvanced(
    advancedFindProductsDto: AdvancedFindProductsDto,
  ): Promise<[Product[] | any[], number]> {
    // Create query builder
    const queryBuilder = this.productRepository.createQueryBuilder('product');

    // Add default sort if no sort criteria provided
    if (
      !advancedFindProductsDto.sort ||
      advancedFindProductsDto.sort.length === 0
    ) {
      const defaultField = advancedFindProductsDto.defaultSortField || 'id';
      advancedFindProductsDto.sort = [
        { field: defaultField, order: SortOrder.ASC, alias: 'product' },
      ];
    }

    // Add joins for related entities if needed
    if (
      advancedFindProductsDto.select?.some((field) =>
        field.includes('category.'),
      )
    ) {
      queryBuilder.leftJoin('product.category', 'category');
    }

    // Get results with advanced filter
    return this.advancedFilterService.getResultsWithAdvancedFilter(
      queryBuilder,
      advancedFindProductsDto,
      'product',
    );
  }
}
