import { IBaseRepository } from '@core/database/base/base.repository.interface';
import { Product } from '@modules/products/entities/product.entity';
import { FindProductsDto } from '@modules/products/dto/find-products.dto';
import { AdvancedFindProductsDto } from '@modules/products/dto/advanced-find-products.dto';

/**
 * Product repository interface
 * This interface extends the base repository interface and adds product-specific methods
 * When switching to MongoDB, you would implement this interface with MongoDB-specific code
 */
export interface IProductRepository extends IBaseRepository<Product> {
  /**
   * Find a product by name
   * @param name Product name
   * @returns Promise with the found product or null
   */
  findByName(name: string): Promise<Product | null>;

  /**
   * Find all active products
   * @returns Promise with array of active products
   */
  findActiveProducts(): Promise<Product[]>;

  /**
   * Find products with advanced filtering, sorting and pagination
   * @param findProductsDto DTO with filter, sort and pagination options
   * @returns Promise with products and count
   */
  findProducts(findProductsDto: FindProductsDto): Promise<[Product[], number]>;

  /**
   * Find products with complex filtering, sorting and pagination
   * @param advancedFindProductsDto Advanced DTO with complex filter, sort and pagination options
   * @returns Promise with products and count
   */
  findProductsAdvanced(
    advancedFindProductsDto: AdvancedFindProductsDto,
  ): Promise<[Product[], number]>;

  /**
   * Find products with complex filtering, sorting and pagination
   * @param advancedFindProductsDto Advanced DTO with complex filter, sort and pagination options
   * @returns Promise with products and count
   */
  findProductsAdvanced(
    advancedFindProductsDto: AdvancedFindProductsDto,
  ): Promise<[Product[], number]>;
}

/**
 * Product repository token for dependency injection
 */
export const PRODUCT_REPOSITORY = 'PRODUCT_REPOSITORY';
