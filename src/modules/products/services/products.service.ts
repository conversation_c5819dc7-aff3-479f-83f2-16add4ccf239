import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { CreateProductDto } from '@modules/products/dto/create-product.dto';
import { UpdateProductDto } from '@modules/products/dto/update-product.dto';
import { FindProductsDto } from '@modules/products/dto/find-products.dto';
import { AdvancedFindProductsDto } from '@modules/products/dto/advanced-find-products.dto';
import { ProductResponseDto } from '@modules/products/dto/product-response.dto';
import { Product } from '@modules/products/entities/product.entity';
import {
  IProductRepository,
  PRODUCT_REPOSITORY,
} from '@modules/products/repositories/product.repository.interface';
import { PageMetaDto } from '@common/dto/page-meta.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';

@Injectable()
export class ProductsService {
  constructor(
    @Inject(PRODUCT_REPOSITORY)
    private readonly productRepository: IProductRepository,
  ) {}

  async create(createProductDto: CreateProductDto): Promise<Product> {
    return this.productRepository.create(createProductDto);
  }

  async findAll(): Promise<Product[]> {
    return this.productRepository.findAll();
  }

  /**
   * Find products with advanced filtering, sorting and pagination
   * @param findProductsDto DTO with filter, sort and pagination options
   * @returns Promise with paginated products
   */
  async findProducts(
    findProductsDto: FindProductsDto,
  ): Promise<ApiResponseDto<ProductResponseDto>> {
    // Get products with pagination, filtering and sorting
    const [products, totalItems] =
      await this.productRepository.findProducts(findProductsDto);

    // Create page metadata
    const pageMetaDto = new PageMetaDto({
      paginationDto: findProductsDto,
      totalItems,
    });

    // Map products to response DTOs
    const productResponseDtos = products.map(
      (product) => new ProductResponseDto(product),
    );

    // Return paginated response
    return new ApiResponseDto(
      productResponseDtos,
      pageMetaDto,
      'Products retrieved successfully',
    );
  }

  async findActive(): Promise<Product[]> {
    console.log('Finding active products...');
    return this.productRepository.findActiveProducts();
  }

  async findOne(id: number): Promise<Product> {
    const product = await this.productRepository.findOneById(id);
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    return product;
  }

  async update(
    id: number,
    updateProductDto: UpdateProductDto,
  ): Promise<Product> {
    const product = await this.findOne(id);
    return this.productRepository.update(product.id, updateProductDto);
  }

  async remove(id: number): Promise<void> {
    const product = await this.findOne(id);
    return this.productRepository.remove(product.id);
  }

  /**
   * Find products with complex filtering, sorting and pagination
   * @param advancedFindProductsDto Advanced DTO with complex filter, sort and pagination options
   * @returns Promise with paginated products
   */
  async findProductsAdvanced(
    advancedFindProductsDto: AdvancedFindProductsDto,
  ): Promise<ApiResponseDto<ProductResponseDto | any>> {
    // Get products with complex filtering, sorting and pagination
    const [products, totalItems] =
      await this.productRepository.findProductsAdvanced(
        advancedFindProductsDto,
      );

    // Create page metadata
    const pageMetaDto = new PageMetaDto({
      paginationDto: advancedFindProductsDto,
      totalItems,
    });

    // If raw results are requested, return them directly
    if (advancedFindProductsDto.raw) {
      return new ApiResponseDto(
        products,
        pageMetaDto,
        'Raw products data retrieved successfully',
      );
    }

    // Map products to response DTOs
    const productResponseDtos = products.map(
      (product) => new ProductResponseDto(product),
    );

    // Return paginated response
    return new ApiResponseDto(
      productResponseDtos,
      pageMetaDto,
      'Products retrieved successfully with advanced filtering',
    );
  }
}
