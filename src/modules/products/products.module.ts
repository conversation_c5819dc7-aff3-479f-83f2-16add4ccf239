import { Module } from '@nestjs/common';
import { DatabaseModule } from '@core/database/database.module';
import { CommonModule } from '@common/common.module';
import { ProductsController } from '@modules/products/controllers/products.controller';
import { ProductRepository } from '@modules/products/repositories/product.repository';
import { PRODUCT_REPOSITORY } from '@modules/products/repositories/product.repository.interface';
import { ProductsService } from '@modules/products/services/products.service';

/**
 * Products module
 * This module provides product-related functionality
 *
 * The repository is injected using a token, which makes it easy to switch
 * to a different implementation (e.g., MongoDB) in the future
 */
@Module({
  imports: [DatabaseModule, CommonModule],
  controllers: [ProductsController],
  providers: [
    ProductsService,
    {
      provide: PRODUCT_REPOSITORY,
      useClass: ProductRepository, // To switch to MongoDB, change this to MongoProductRepository
    },
  ],
  exports: [ProductsService],
})
export class ProductsModule {}
