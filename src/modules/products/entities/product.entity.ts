import { Column, Entity } from 'typeorm';
import { BaseEntity } from '@core/database/base/base.entity';

/**
 * Product entity
 * This entity represents a product in the system
 */
@Entity('products')
export class Product extends BaseEntity {
  @Column()
  name: string;

  @Column()
  description: string;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column({ default: 0 })
  stock: number;

  @Column({ default: true })
  isActive: boolean;
}
