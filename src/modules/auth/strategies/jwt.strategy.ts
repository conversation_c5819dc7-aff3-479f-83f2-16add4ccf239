import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { UsersService } from '@modules/users/services/users.service';
import { RedisService } from '@core/redis/redis.service';
import { JwtPayload } from '@modules/auth/interfaces/jwt-payload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    configService: ConfigService,
    private readonly usersService: UsersService,
    private readonly redisService: RedisService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('jwt.secret') || 'your-secret-key',
    });
  }

  async validate(payload: JwtPayload) {
    // Check if token is for access
    if (payload.tokenType !== 'access') {
      throw new UnauthorizedException('Invalid token type');
    }

    // We can't get the raw token from the request at this point
    // But we can check if the user's tokens are valid in Redis
    // This is a simplified approach - in a real app, you'd need to track tokens better
    if (payload.deviceId) {
      const storedToken = await this.redisService.getRefreshToken(
        payload.deviceId,
      );
      if (!storedToken) {
        // If there's no refresh token for this device, the user might have logged out
        throw new UnauthorizedException('Session expired or revoked');
      }
    }

    const user = await this.usersService.findOne(payload.sub);

    if (!user || !user.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    return {
      id: payload.sub,
      email: payload.email,
      name: payload.name,
      deviceId: payload.deviceId,
    };
  }
}
