import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { AuthService } from '@modules/auth/services/auth.service';
import { LoginDto } from '@modules/auth/dto/login.dto';
import { RegisterDto } from '@modules/auth/dto/register.dto';
import { RefreshTokenDto } from '@modules/auth/dto/refresh-token.dto';
import { LogoutDto } from '@modules/auth/dto/logout.dto';
import { AuthResponseDto } from '@modules/auth/dto/auth-response.dto';
import { FirebaseLoginDto } from '@modules/auth/dto/firebase-login.dto';
import { UserProfileResponseDto } from '@modules/auth/dto/user-profile-response.dto';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { User } from '@modules/users/entities/user.entity';
import { CurrentUser } from '@common/decorators/current-user.decorator';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({
    status: 201,
    description: 'User successfully registered',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
  })
  register(
    @Body() registerDto: RegisterDto,
    @Req() req: Request,
  ): Promise<AuthResponseDto> {
    return this.authService.register(registerDto, req);
  }

  @Post('login')
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged in',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  login(
    @Body() loginDto: LoginDto,
    @Req() req: Request,
  ): Promise<AuthResponseDto> {
    return this.authService.login(loginDto, req);
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get current user profile with roles and permissions',
  })
  @ApiResponse({
    status: 200,
    description: 'User profile successfully retrieved',
    type: UserProfileResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  getMe(@CurrentUser() user: User): Promise<UserProfileResponseDto> {
    return this.authService.getMe(user);
  }

  @Post('refresh')
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({
    status: 200,
    description: 'Token successfully refreshed',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Invalid refresh token',
  })
  refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<AuthResponseDto> {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged out',
  })
  logout(
    @CurrentUser() user: User,
    @Body() logoutDto: LogoutDto,
  ): Promise<{ success: boolean }> {
    return this.authService.logout(user.id, logoutDto);
  }

  @Post('logout-all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout from all devices' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged out from all devices',
  })
  logoutAll(@CurrentUser() user: User): Promise<{ success: boolean }> {
    return this.authService.logoutAll(user.id);
  }

  @Post('logout-all-except-current')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout from all devices except current' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged out from all devices except current',
  })
  logoutAllExceptCurrent(
    @CurrentUser() user: User,
    @Body() logoutDto: LogoutDto,
  ): Promise<{ success: boolean }> {
    return this.authService.logoutAllExceptCurrent(user.id, logoutDto.deviceId);
  }

  @Post('/login-google')
  @ApiOperation({ summary: 'Login with Firebase Google token' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged in with Firebase',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid Firebase token',
  })
  firebaseGoogleLogin(
    @Body() firebaseLoginDto: FirebaseLoginDto,
    @Req() req: Request,
  ): Promise<AuthResponseDto> {
    return this.authService.loginGoogle(firebaseLoginDto, req);
  }
}
