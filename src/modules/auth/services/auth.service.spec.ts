import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersService } from '@modules/users/services/users.service';
import { RedisService } from '@core/redis/redis.service';
import { FirebaseService } from '@core/firebase/firebase.service';
import { FirebaseLoginDto } from '../dto/firebase-login.dto';

describe('AuthService - Firebase Google Login', () => {
  let authService: AuthService;
  let firebaseService: FirebaseService;
  let usersService: UsersService;

  const mockFirebaseService = {
    verifyIdToken: jest.fn(),
    isConfigured: jest.fn().mockReturnValue(true),
  };

  const mockUsersService = {
    findOrCreateFirebaseUser: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
    decode: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockRedisService = {
    storeRefreshToken: jest.fn(),
    isTokenBlacklisted: jest.fn(),
    blacklistToken: jest.fn(),
    removeRefreshToken: jest.fn(),
    removeAllRefreshTokens: jest.fn(),
    removeAllRefreshTokensExceptCurrent: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: FirebaseService,
          useValue: mockFirebaseService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
      ],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    firebaseService = module.get<FirebaseService>(FirebaseService);
    usersService = module.get<UsersService>(UsersService);
  });

  describe('firebaseGoogleLogin', () => {
    const mockFirebaseLoginDto: FirebaseLoginDto = {
      idToken: 'mock-firebase-id-token',
    };

    const mockDecodedToken = {
      uid: 'firebase-uid-123',
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'https://example.com/avatar.jpg',
    };

    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      avatar: 'https://example.com/avatar.jpg',
      firebaseUid: 'firebase-uid-123',
    };

    beforeEach(() => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'jwt.secret':
            return 'test-secret';
          case 'jwt.accessToken.expiresIn':
            return '15m';
          case 'jwt.refreshToken.expiresIn':
            return '7d';
          default:
            return undefined;
        }
      });

      mockJwtService.sign.mockReturnValue('mock-jwt-token');
      mockRedisService.storeRefreshToken.mockResolvedValue(undefined);
    });

    it('should successfully authenticate with valid Firebase token', async () => {
      // Arrange
      mockFirebaseService.verifyIdToken.mockResolvedValue(mockDecodedToken);
      mockUsersService.findOrCreateFirebaseUser.mockResolvedValue(mockUser);

      // Act
      const result = await authService.loginGoogle(mockFirebaseLoginDto);

      // Assert
      expect(firebaseService.verifyIdToken).toHaveBeenCalledWith(
        mockFirebaseLoginDto.idToken,
      );
      expect(usersService.findOrCreateFirebaseUser).toHaveBeenCalledWith({
        uid: mockDecodedToken.uid,
        email: mockDecodedToken.email,
        name: mockDecodedToken.name,
        picture: mockDecodedToken.picture,
      });
      expect(result).toEqual({
        accessToken: 'mock-jwt-token',
        refreshToken: 'mock-jwt-token',
        deviceId: expect.any(String),
        userId: mockUser.id,
        email: mockUser.email,
        name: mockUser.name,
        avatar: mockUser.avatar,
      });
    });

    it('should throw UnauthorizedException when Firebase token is invalid', async () => {
      // Arrange
      mockFirebaseService.verifyIdToken.mockRejectedValue(
        new Error('Invalid token'),
      );

      // Act & Assert
      await expect(
        authService.loginGoogle(mockFirebaseLoginDto),
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException when email is missing from token', async () => {
      // Arrange
      const tokenWithoutEmail = { ...mockDecodedToken, email: undefined };
      mockFirebaseService.verifyIdToken.mockResolvedValue(tokenWithoutEmail);

      // Act & Assert
      await expect(
        authService.loginGoogle(mockFirebaseLoginDto),
      ).rejects.toThrow(UnauthorizedException);
    });
  });
});
