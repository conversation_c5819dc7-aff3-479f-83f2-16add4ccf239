import {
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcrypt';

import { UsersService } from '@modules/users/services/users.service';
import { RedisService } from '@core/redis/redis.service';
import { FirebaseService } from '@core/firebase/firebase.service';
import { UserRoleService } from '@modules/authorization/services/user-role.service';
import { LoginDto } from '@modules/auth/dto/login.dto';
import { RegisterDto } from '@modules/auth/dto/register.dto';
import { RefreshTokenDto } from '@modules/auth/dto/refresh-token.dto';
import { LogoutDto } from '@modules/auth/dto/logout.dto';
import { AuthResponseDto } from '@modules/auth/dto/auth-response.dto';
import { FirebaseLoginDto } from '@modules/auth/dto/firebase-login.dto';
import { UserProfileResponseDto } from '@modules/auth/dto/user-profile-response.dto';
import { JwtPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { User } from '@modules/users/entities/user.entity';
import { UserTypeEnum } from '@app/modules/users/user.constant';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly firebaseService: FirebaseService,
    private readonly userRoleService: UserRoleService,
  ) {}

  async register(
    registerDto: RegisterDto,
    req?: Request,
  ): Promise<AuthResponseDto> {
    const { email, password } = registerDto;

    // Check if user already exists
    const existingUser = await this.usersService.findByEmail(email);
    if (existingUser) {
      throw new UnauthorizedException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await this.hashPassword(password);

    // Create user
    const user = await this.usersService.create({
      ...registerDto,
      password: hashedPassword,
    });

    // Generate device ID
    const deviceId = uuidv4();

    // Generate tokens
    const { accessToken, refreshToken } = await this.generateTokens(
      user,
      deviceId,
    );

    // Store refresh token
    await this.storeRefreshToken(user.id, deviceId, refreshToken, req);

    return {
      accessToken,
      refreshToken,
      deviceId,
      userId: user.id,
      email: user.email,
      name: user.name,
    };
  }

  async login(loginDto: LoginDto, req?: Request): Promise<AuthResponseDto> {
    const { email, password } = loginDto;

    // Find user by email with password field included
    const user = await this.usersService.findByEmailWithPassword(email);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if it's a Google account (Firebase users)
    if (user.type === UserTypeEnum.Google && !user.password) {
      throw new UnauthorizedException(
        'Please login with Google authentication',
      );
    }

    // Verify password
    const isPasswordValid = await this.comparePasswords(
      password,
      user.password,
    );
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Generate device ID
    const deviceId = uuidv4();

    // Generate tokens
    const { accessToken, refreshToken } = await this.generateTokens(
      user,
      deviceId,
    );

    // Store refresh token
    await this.storeRefreshToken(user.id, deviceId, refreshToken, req);

    return {
      accessToken,
      refreshToken,
      deviceId,
      userId: user.id,
      email: user.email,
      name: user.name,
    };
  }

  async getMe(user: User): Promise<UserProfileResponseDto> {
    // Get user roles and permissions
    const [roles, permissions] = await Promise.all([
      this.userRoleService.getRolesForUser(user.id),
      this.userRoleService.getPermissionsForUser(user.id),
    ]);

    return new UserProfileResponseDto(user, roles, permissions);
  }

  async loginGoogle(
    firebaseLoginDto: FirebaseLoginDto,
    req?: Request,
  ): Promise<AuthResponseDto> {
    try {
      // Verify Firebase ID token
      const decodedToken = await this.firebaseService.verifyIdToken(
        firebaseLoginDto.idToken,
      );

      // Extract user information from Firebase token
      if (!decodedToken.email) {
        throw new UnauthorizedException(
          'Email is required from Firebase token',
        );
      }

      const firebaseUser = {
        uid: decodedToken.uid,
        email: decodedToken.email,
        name: decodedToken.name || decodedToken.email.split('@')[0],
        picture: decodedToken.picture,
      };

      // Find or create user
      const user =
        await this.usersService.findOrCreateFirebaseUser(firebaseUser);

      // Generate device ID
      const deviceId = uuidv4();

      // Generate tokens
      const { accessToken, refreshToken } = await this.generateTokens(
        user,
        deviceId,
      );

      // Store refresh token
      await this.storeRefreshToken(user.id, deviceId, refreshToken, req);

      return {
        accessToken,
        refreshToken,
        deviceId,
        userId: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid Firebase token');
    }
  }

  async refreshToken(
    refreshTokenDto: RefreshTokenDto,
  ): Promise<AuthResponseDto> {
    const { refreshToken, deviceId } = refreshTokenDto;

    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('jwt.secret'),
      });

      // Check if token is for refresh
      if (payload.tokenType !== 'refresh') {
        throw new ForbiddenException('Invalid token type');
      }

      // Check if device ID matches
      if (payload.deviceId !== deviceId) {
        throw new ForbiddenException('Invalid device ID');
      }

      // Check if token is blacklisted
      const isBlacklisted =
        await this.redisService.isTokenBlacklisted(refreshToken);
      if (isBlacklisted) {
        throw new ForbiddenException('Token has been revoked');
      }

      // Get user
      const user = await this.usersService.findOne(payload.sub);
      if (!user) {
        throw new ForbiddenException('User not found');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user, deviceId);

      // Blacklist old refresh token
      await this.redisService.blacklistToken(
        refreshToken,
        this.getTokenExpirationTime(refreshToken),
      );

      // Store new refresh token
      await this.storeRefreshToken(user.id, deviceId, tokens.refreshToken);

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        deviceId,
        userId: user.id,
        email: user.email,
        name: user.name,
      };
    } catch (error) {
      if (
        error.name === 'JsonWebTokenError' ||
        error.name === 'TokenExpiredError'
      ) {
        throw new ForbiddenException('Invalid token');
      }
      throw error;
    }
  }

  async logout(
    userId: number,
    logoutDto: LogoutDto,
  ): Promise<{ success: boolean }> {
    const { deviceId } = logoutDto;

    // Remove refresh token from Redis
    await this.redisService.removeRefreshToken(userId, deviceId);

    return { success: true };
  }

  async logoutAll(userId: number): Promise<{ success: boolean }> {
    // Remove all refresh tokens from Redis
    await this.redisService.removeAllRefreshTokens(userId);

    return { success: true };
  }

  async logoutAllExceptCurrent(
    userId: number,
    deviceId: string,
  ): Promise<{ success: boolean }> {
    // Remove all refresh tokens except current from Redis
    await this.redisService.removeAllRefreshTokensExceptCurrent(
      userId,
      deviceId,
    );

    return { success: true };
  }

  private async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt();
    return bcrypt.hash(password, salt);
  }

  private async comparePasswords(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  private async generateTokens(
    user: User,
    deviceId: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    // Create payload for access token
    const accessTokenPayload: JwtPayload = {
      sub: user.id,
      email: user.email,
      name: user.name,
      deviceId,
      tokenType: 'access',
    };

    // Create payload for refresh token
    const refreshTokenPayload: JwtPayload = {
      sub: user.id,
      email: user.email,
      name: user.name,
      deviceId,
      tokenType: 'refresh',
    };

    // Generate tokens
    const accessToken = this.jwtService.sign(accessTokenPayload, {
      secret: this.configService.get<string>('jwt.secret'),
      expiresIn: this.configService.get<string>('jwt.accessToken.expiresIn'),
    });

    const refreshToken = this.jwtService.sign(refreshTokenPayload, {
      secret: this.configService.get<string>('jwt.secret'),
      expiresIn: this.configService.get<string>('jwt.refreshToken.expiresIn'),
    });

    return { accessToken, refreshToken };
  }

  private async storeRefreshToken(
    userId: number,
    deviceId: string,
    token: string,
    req?: Request,
  ): Promise<void> {
    // Calculate expiration time
    const expiresIn = this.getTokenExpirationTime(token);

    // Store in Redis
    await this.redisService.storeRefreshToken(
      userId,
      deviceId,
      token,
      expiresIn,
    );

    // // Log for debugging (optional)
    // console.log(
    //   `Stored refresh token for user ${userId} on device ${deviceId}`,
    // );
    // if (req) {
    //   console.log(`IP: ${req.ip}, User-Agent: ${req.headers['user-agent']}`);
    // }
  }

  private getTokenExpirationTime(token: string): number {
    try {
      const decoded = this.jwtService.decode(token);
      if (decoded && typeof decoded === 'object' && decoded.exp) {
        const expirationTime = decoded.exp - Math.floor(Date.now() / 1000);
        return expirationTime > 0 ? expirationTime : 0;
      }
      return 0;
    } catch (error) {
      return 0;
    }
  }
}
