import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class AuthResponseDto {
  @ApiProperty({
    description: 'Access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'Device ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  deviceId: string;

  @ApiProperty({
    description: 'User ID',
    example: 1,
  })
  userId: number;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User name',
    example: '<PERSON>',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'User avatar URL',
    example: 'https://lh3.googleusercontent.com/a/photo.jpg',
  })
  avatar?: string;

  @ApiPropertyOptional({
    description: 'Whether the user account was created via Google',
    example: true,
  })
  isGoogleAccount?: boolean;
}
