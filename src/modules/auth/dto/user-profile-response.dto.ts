import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { User } from '@modules/users/entities/user.entity';
import { RoleResponseDto } from '@modules/authorization/dto/role/role-response.dto';
import { PermissionResponseDto } from '@modules/authorization/dto/permission/permission-response.dto';
import { UserTypeEnum } from '@modules/users/user.constant';

export class UserProfileResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: 1,
  })
  userId: number;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User name',
    example: '<PERSON>',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'User phone number',
    example: '+1234567890',
  })
  phoneNumber?: string;

  @ApiProperty({
    description: 'User type',
    enum: UserTypeEnum,
    example: UserTypeEnum.Local,
  })
  type: UserTypeEnum;

  @ApiProperty({
    description: 'User active status',
    example: true,
  })
  isActive: boolean;

  @ApiPropertyOptional({
    description: 'User avatar URL',
    example: 'https://lh3.googleusercontent.com/a/photo.jpg',
  })
  avatar?: string;

  @ApiProperty({
    description: 'Whether the user is a super admin',
    example: false,
  })
  isSuperAdmin: boolean;

  @ApiPropertyOptional({
    description: 'Firebase UID for Google accounts',
    example: 'firebase_uid_123',
  })
  firebaseUid?: string;

  @ApiProperty({
    description: 'User creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'User last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'User roles',
    type: [RoleResponseDto],
  })
  roles: RoleResponseDto[];

  @ApiProperty({
    description: 'User permissions',
    type: [PermissionResponseDto],
  })
  permissions: PermissionResponseDto[];

  constructor(
    user: User,
    roles: RoleResponseDto[] = [],
    permissions: PermissionResponseDto[] = [],
  ) {
    this.userId = user.id;
    this.email = user.email;
    this.name = user.name;
    this.phoneNumber = user.phoneNumber;
    this.type = user.type;
    this.isActive = user.isActive;
    this.avatar = user.avatar;
    this.isSuperAdmin = user.isSuperAdmin;
    this.firebaseUid = user.firebaseUid;
    this.createdAt = user.createdAt;
    this.updatedAt = user.updatedAt;
    this.roles = roles;
    this.permissions = permissions;
  }
}
