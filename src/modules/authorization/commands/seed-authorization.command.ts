import { Command, CommandRunner } from 'nest-commander';
import { AuthorizationSeeder } from '../authorization.seeder';

@Command({
  name: 'seed:authorization',
  description: 'Seed authorization data (roles, permissions, etc.)',
})
export class SeedAuthorizationCommand extends CommandRunner {
  constructor(private readonly authorizationSeeder: AuthorizationSeeder) {
    super();
  }

  async run(): Promise<void> {
    try {
      await this.authorizationSeeder.seed();
      console.log('Authorization data seeded successfully');
    } catch (error) {
      console.error('Error seeding authorization data:', error);
    }
  }
}
