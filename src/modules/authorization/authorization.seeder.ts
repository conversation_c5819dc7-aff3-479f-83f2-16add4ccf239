import { Inject, Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { PermissionGroup } from './entities/permission-group.entity';
import { Permission } from './entities/permission.entity';
import { Role } from './entities/role.entity';
import { RolePermission } from './entities/role-permission.entity';
import { PermissionDependency } from './entities/permission-dependency.entity';
import { User } from '@modules/users/entities/user.entity';
import {
  permissionGroups,
  permissions,
  roles,
  rolePermissionsMap,
  permissionDependencies,
  superAdmin,
} from './seeds';
import { DATABASE_CONNECTION } from '@core/database/database.providers';

@Injectable()
export class AuthorizationSeeder {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly dataSource: DataSource,
  ) {}

  async seed(): Promise<void> {
    await this.seedPermissionGroups();
    await this.seedPermissions();
    await this.seedRoles();
    await this.seedRolePermissions();
    await this.seedPermissionDependencies();
    await this.seedSuperAdmin();
  }

  private async seedPermissionDependencies(): Promise<void> {
    const permissionDependencyRepository =
      this.dataSource.getRepository(PermissionDependency);
    const permissionRepository = this.dataSource.getRepository(Permission);

    // Check if permission dependencies already exist
    const count = await permissionDependencyRepository.count();
    if (count > 0) {
      console.log('Permission dependencies already seeded');
      return;
    }

    // Process permission dependencies
    for (const dependency of permissionDependencies) {
      // Find permission by code
      const permission = await permissionRepository.findOne({
        where: { code: dependency.permissionCode },
      });

      if (!permission) {
        console.log(`Permission ${dependency.permissionCode} not found`);
        continue;
      }

      // Find dependent permission by code
      const dependentPermission = await permissionRepository.findOne({
        where: { code: dependency.dependentPermissionCode },
      });

      if (!dependentPermission) {
        console.log(
          `Dependent permission ${dependency.dependentPermissionCode} not found`,
        );
        continue;
      }

      // Create permission dependency
      await permissionDependencyRepository.insert({
        permissionId: permission.id,
        dependentPermissionId: dependentPermission.id,
      });
    }

    console.log('Permission dependencies seeded successfully');
  }

  private async seedSuperAdmin(): Promise<void> {
    const userRepository = this.dataSource.getRepository(User);

    // Check if super admin already exists
    const existingSuperAdmin = await userRepository.findOne({
      where: { email: superAdmin.email },
    });

    if (existingSuperAdmin) {
      console.log('Super admin already exists');
      return;
    }

    // Create super admin
    await userRepository.insert(superAdmin);
    console.log('Super admin created successfully');
  }

  private async seedPermissionGroups(): Promise<void> {
    const permissionGroupRepository =
      this.dataSource.getRepository(PermissionGroup);

    // Check if permission groups already exist
    const count = await permissionGroupRepository.count();
    if (count > 0) {
      console.log('Permission groups already seeded');
      return;
    }

    // Insert permission groups
    await permissionGroupRepository.insert(permissionGroups);
    console.log('Permission groups seeded successfully');
  }

  private async seedPermissions(): Promise<void> {
    const permissionRepository = this.dataSource.getRepository(Permission);

    // Check if permissions already exist
    const count = await permissionRepository.count();
    if (count > 0) {
      console.log('Permissions already seeded');
      return;
    }

    // Insert permissions
    await permissionRepository.insert(permissions);
    console.log('Permissions seeded successfully');
  }

  private async seedRoles(): Promise<void> {
    const roleRepository = this.dataSource.getRepository(Role);

    // Check if roles already exist
    const count = await roleRepository.count();
    if (count > 0) {
      console.log('Roles already seeded');
      return;
    }

    // Insert roles
    await roleRepository.insert(roles);
    console.log('Roles seeded successfully');
  }

  private async seedRolePermissions(): Promise<void> {
    const rolePermissionRepository =
      this.dataSource.getRepository(RolePermission);
    const roleRepository = this.dataSource.getRepository(Role);
    const permissionRepository = this.dataSource.getRepository(Permission);

    // Check if role permissions already exist
    const count = await rolePermissionRepository.count();
    if (count > 0) {
      console.log('Role permissions already seeded');
      return;
    }

    // Process role permissions
    for (const rolePermissionData of rolePermissionsMap) {
      // Find role by name
      const role = await roleRepository.findOne({
        where: { name: rolePermissionData.role },
      });

      if (!role) {
        console.log(`Role ${rolePermissionData.role} not found`);
        continue;
      }

      // Find permissions by code
      for (const permissionCode of rolePermissionData.permissions) {
        const permission = await permissionRepository.findOne({
          where: { code: permissionCode },
        });

        if (!permission) {
          console.log(`Permission ${permissionCode} not found`);
          continue;
        }

        // Create role permission
        await rolePermissionRepository.insert({
          roleId: role.id,
          permissionId: permission.id,
        });
      }
    }

    console.log('Role permissions seeded successfully');
  }
}
