import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSION_KEY } from '../decorators/require-permission.decorator';
import {
  IUserRoleRepository,
  USER_ROLE_REPOSITORY,
} from '../repositories/user-role.repository.interface';
import {
  IPermissionRepository,
  PERMISSION_REPOSITORY,
} from '../repositories/permission.repository.interface';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @Inject(USER_ROLE_REPOSITORY)
    private readonly userRoleRepository: IUserRoleRepository,
    @Inject(PERMISSION_REPOSITORY)
    private readonly permissionRepository: IPermissionRepository,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermission = this.reflector.getAllAndOverride<string>(
      PERMISSION_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermission) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Super Admin always has all permissions
    if (user.isSuperAdmin) {
      return true;
    }

    // Check if the permission is public
    const permission =
      await this.permissionRepository.findByCode(requiredPermission);

    if (permission?.isPublic) {
      return true;
    }

    // Check if the user has the required permission or any permission that implies it
    const hasPermission =
      await this.userRoleRepository.hasPermissionWithImplied(
        user.id,
        requiredPermission,
      );

    if (!hasPermission) {
      throw new UnauthorizedException(
        `You don't have permission to access this resource`,
      );
    }

    return true;
  }
}
