import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';
import { Permission } from '../../entities/permission.entity';
import { PermissionGroupResponseDto } from '../permission-group/permission-group-response.dto';

@Exclude()
export class PermissionResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Permission ID',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Permission code',
    example: 'users.create',
  })
  code: string;

  @Expose()
  @ApiProperty({
    description: 'Permission name',
    example: 'Create User',
  })
  name: string;

  @Expose()
  @ApiPropertyOptional({
    description: 'Permission description',
    example: 'Allows creating new users',
  })
  description?: string;

  @Expose()
  @ApiProperty({
    description: 'Display order for sorting',
    example: 1,
  })
  displayOrder: number;

  @Expose()
  @ApiProperty({
    description: 'Whether this permission is public (accessible without authentication)',
    example: false,
  })
  isPublic: boolean;

  @Expose()
  @ApiPropertyOptional({
    description: 'Permission group ID',
    example: 1,
  })
  groupId?: number;

  @Expose()
  @ApiPropertyOptional({
    description: 'Permission group',
    type: () => PermissionGroupResponseDto,
  })
  @Type(() => PermissionGroupResponseDto)
  group?: PermissionGroupResponseDto;

  constructor(permission: Permission) {
    Object.assign(this, permission);

    if (permission.group) {
      this.group = new PermissionGroupResponseDto(permission.group);
    }
  }
}
