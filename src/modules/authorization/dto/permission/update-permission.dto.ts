import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsBoolean } from 'class-validator';

export class UpdatePermissionDto {
  @ApiPropertyOptional({
    description: 'Permission code',
    example: 'users.create',
  })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiPropertyOptional({
    description: 'Permission name',
    example: 'Create User',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Permission description',
    example: 'Allows creating new users',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Display order for sorting',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  displayOrder?: number;

  @ApiPropertyOptional({
    description: 'Whether this permission is public (accessible without authentication)',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;

  @ApiPropertyOptional({
    description: 'Permission group ID',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  groupId?: number;
}
