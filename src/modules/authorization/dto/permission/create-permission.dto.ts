import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsBoolean } from 'class-validator';

export class CreatePermissionDto {
  @ApiProperty({
    description: 'Permission code',
    example: 'users.create',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'Permission name',
    example: 'Create User',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Permission description',
    example: 'Allows creating new users',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Display order for sorting',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  displayOrder?: number;

  @ApiPropertyOptional({
    description: 'Whether this permission is public (accessible without authentication)',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;

  @ApiPropertyOptional({
    description: 'Permission group ID',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  groupId?: number;
}
