import { ApiProperty } from '@nestjs/swagger';

export class PermissionDependencyResponseDto {
  @ApiProperty({
    description: 'Permission dependency ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Permission ID',
    example: 1,
  })
  permissionId: number;

  @ApiProperty({
    description: 'Dependent permission ID',
    example: 2,
  })
  dependentPermissionId: number;

  @ApiProperty({
    description: 'Permission code',
    example: 'products.create',
  })
  permissionCode?: string;

  @ApiProperty({
    description: 'Dependent permission code',
    example: 'categories.read',
  })
  dependentPermissionCode?: string;

  @ApiProperty({
    description: 'Created at',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated at',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}
