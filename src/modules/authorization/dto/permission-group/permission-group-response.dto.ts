import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';
import { PermissionGroup } from '../../entities/permission-group.entity';
import { PermissionResponseDto } from '../permission/permission-response.dto';

@Exclude()
export class PermissionGroupResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Permission group ID',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Permission group name',
    example: 'User Management',
  })
  name: string;

  @Expose()
  @ApiPropertyOptional({
    description: 'Permission group description',
    example: 'Permissions related to user management',
  })
  description?: string;

  @Expose()
  @ApiProperty({
    description: 'Display order for sorting',
    example: 1,
  })
  displayOrder: number;

  @Expose()
  @ApiPropertyOptional({
    description: 'Permissions in this group',
    type: [PermissionResponseDto],
  })
  @Type(() => PermissionResponseDto)
  permissions?: PermissionResponseDto[];

  constructor(permissionGroup: PermissionGroup) {
    Object.assign(this, permissionGroup);

    if (permissionGroup.permissions) {
      this.permissions = permissionGroup.permissions.map(
        permission => new PermissionResponseDto(permission),
      );
    }
  }
}
