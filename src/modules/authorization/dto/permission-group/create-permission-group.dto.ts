import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber } from 'class-validator';

export class CreatePermissionGroupDto {
  @ApiProperty({
    description: 'Permission group name',
    example: 'User Management',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Permission group description',
    example: 'Permissions related to user management',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Display order for sorting',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  displayOrder?: number;
}
