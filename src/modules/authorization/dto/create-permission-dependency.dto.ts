import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreatePermissionDependencyDto {
  @ApiProperty({
    description: 'Permission code',
    example: 'products.create',
  })
  @IsString()
  @IsNotEmpty()
  permissionCode: string;

  @ApiProperty({
    description: 'Dependent permission code',
    example: 'categories.read',
  })
  @IsString()
  @IsNotEmpty()
  dependentPermissionCode: string;
}
