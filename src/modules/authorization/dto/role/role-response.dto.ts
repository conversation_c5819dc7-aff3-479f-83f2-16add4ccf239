import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';
import { Role } from '../../entities/role.entity';
import { PermissionResponseDto } from '../permission/permission-response.dto';

@Exclude()
export class RoleResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Role ID',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Role name',
    example: 'Admin',
  })
  name: string;

  @Expose()
  @ApiPropertyOptional({
    description: 'Role description',
    example: 'Administrator with full access',
  })
  description?: string;

  @Expose()
  @ApiProperty({
    description: 'Whether this is the default role for new users',
    example: false,
  })
  isDefault: boolean;

  @Expose()
  @ApiProperty({
    description: 'Whether this is a system role that cannot be modified',
    example: false,
  })
  isSystem: boolean;

  @Expose()
  @ApiPropertyOptional({
    description: 'Permissions assigned to this role',
    type: [PermissionResponseDto],
  })
  @Type(() => PermissionResponseDto)
  permissions?: PermissionResponseDto[];

  constructor(role: Role) {
    Object.assign(this, role);

    if (role.rolePermissions) {
      this.permissions = role.rolePermissions.map(
        rp => new PermissionResponseDto(rp.permission),
      );
    }
  }
}
