import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { DatabaseModule } from '@core/database/database.module';
import { UsersModule } from '@modules/users/users.module';
import { repositoriesProviders } from './providers';
import {
  PermissionGroupService,
  PermissionService,
  RoleService,
  UserRoleService,
  PermissionInitializerService,
  PermissionDependencyService,
} from './services';
import {
  PermissionGroupController,
  PermissionController,
  RoleController,
  UserRoleController,
  PermissionDependencyController,
} from './controllers';
import { PermissionGuard } from './guards';
import { AuthorizationSeeder } from './authorization.seeder';
import { SeedAuthorizationCommand } from './commands';

@Module({
  imports: [DatabaseModule, UsersModule],
  controllers: [
    PermissionGroupController,
    PermissionController,
    RoleController,
    UserRoleController,
    PermissionDependencyController,
  ],
  providers: [
    ...repositoriesProviders,
    PermissionGroupService,
    PermissionService,
    RoleService,
    UserRoleService,
    AuthorizationSeeder,
    SeedAuthorizationCommand,
    PermissionInitializerService,
    PermissionDependencyService,
    {
      provide: APP_GUARD,
      useClass: PermissionGuard,
    },
  ],
  exports: [
    ...repositoriesProviders,
    PermissionGroupService,
    PermissionService,
    RoleService,
    UserRoleService,
    PermissionDependencyService,
    AuthorizationSeeder,
  ],
})
export class AuthorizationModule {}
