import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { RoleService } from '../services/role.service';
import {
  CreateRoleDto,
  UpdateRoleDto,
  RoleResponseDto,
  PermissionResponseDto,
} from '../dto';
import { ParseIdPipe } from '@common/pipes/parse-id.pipe';
import { RequirePermission } from '../decorators/require-permission.decorator';
import { ROLE_PERMISSIONS } from '../constants';

@ApiTags('roles')
@Controller('roles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @RequirePermission(ROLE_PERMISSIONS.CREATE)
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({
    status: 201,
    description: 'The role has been successfully created.',
    type: RoleResponseDto,
  })
  create(@Body() createRoleDto: CreateRoleDto): Promise<RoleResponseDto> {
    return this.roleService.create(createRoleDto);
  }

  @Get()
  @RequirePermission(ROLE_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get all roles' })
  @ApiResponse({
    status: 200,
    description: 'Return all roles',
    type: [RoleResponseDto],
  })
  findAll(): Promise<RoleResponseDto[]> {
    return this.roleService.findAll();
  }

  @Get('with-permissions')
  @RequirePermission(ROLE_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get all roles with their permissions' })
  @ApiResponse({
    status: 200,
    description: 'Return all roles with their permissions',
    type: [RoleResponseDto],
  })
  findAllWithPermissions(): Promise<RoleResponseDto[]> {
    return this.roleService.findAllWithPermissions();
  }

  @Get(':id')
  @RequirePermission(ROLE_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get a role by ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the role with the specified ID',
    type: RoleResponseDto,
  })
  findOne(@Param('id', ParseIdPipe) id: number): Promise<RoleResponseDto> {
    return this.roleService.findOne(id);
  }

  @Get(':id/permissions')
  @RequirePermission(ROLE_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get permissions for a role' })
  @ApiResponse({
    status: 200,
    description: 'Return the permissions for the role with the specified ID',
    type: [PermissionResponseDto],
  })
  getPermissions(
    @Param('id', ParseIdPipe) id: number,
  ): Promise<PermissionResponseDto[]> {
    return this.roleService.getPermissions(id);
  }

  @Patch(':id')
  @RequirePermission(ROLE_PERMISSIONS.UPDATE)
  @ApiOperation({ summary: 'Update a role' })
  @ApiResponse({
    status: 200,
    description: 'The role has been successfully updated.',
    type: RoleResponseDto,
  })
  update(
    @Param('id', ParseIdPipe) id: number,
    @Body() updateRoleDto: UpdateRoleDto,
  ): Promise<RoleResponseDto> {
    return this.roleService.update(id, updateRoleDto);
  }

  @Delete(':id')
  @RequirePermission(ROLE_PERMISSIONS.DELETE)
  @ApiOperation({ summary: 'Delete a role' })
  @ApiResponse({
    status: 200,
    description: 'The role has been successfully deleted.',
  })
  remove(@Param('id', ParseIdPipe) id: number): Promise<void> {
    return this.roleService.remove(id);
  }
}
