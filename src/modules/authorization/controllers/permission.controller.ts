import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { PermissionService } from '../services/permission.service';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionResponseDto,
} from '../dto';
import { ParseIdPipe } from '@common/pipes/parse-id.pipe';
import { RequirePermission } from '../decorators/require-permission.decorator';

@ApiTags('permissions')
@Controller('permissions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @Post()
  @RequirePermission('permissions.create')
  @ApiOperation({ summary: 'Create a new permission' })
  @ApiResponse({
    status: 201,
    description: 'The permission has been successfully created.',
    type: PermissionResponseDto,
  })
  create(
    @Body() createPermissionDto: CreatePermissionDto,
  ): Promise<PermissionResponseDto> {
    return this.permissionService.create(createPermissionDto);
  }

  @Get()
  @RequirePermission('permissions.read')
  @ApiOperation({ summary: 'Get all permissions' })
  @ApiResponse({
    status: 200,
    description: 'Return all permissions',
    type: [PermissionResponseDto],
  })
  findAll(): Promise<PermissionResponseDto[]> {
    return this.permissionService.findAll();
  }

  @Get('with-groups')
  @RequirePermission('permissions.read')
  @ApiOperation({ summary: 'Get all permissions with their groups' })
  @ApiResponse({
    status: 200,
    description: 'Return all permissions with their groups',
    type: [PermissionResponseDto],
  })
  findAllWithGroups(): Promise<PermissionResponseDto[]> {
    return this.permissionService.findAllWithGroups();
  }

  @Get('public')
  @RequirePermission('permissions.read')
  @ApiOperation({ summary: 'Get all public permissions' })
  @ApiResponse({
    status: 200,
    description: 'Return all public permissions',
    type: [PermissionResponseDto],
  })
  findPublicPermissions(): Promise<PermissionResponseDto[]> {
    return this.permissionService.findPublicPermissions();
  }

  @Get(':id')
  @RequirePermission('permissions.read')
  @ApiOperation({ summary: 'Get a permission by ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the permission with the specified ID',
    type: PermissionResponseDto,
  })
  findOne(
    @Param('id', ParseIdPipe) id: number,
  ): Promise<PermissionResponseDto> {
    return this.permissionService.findOne(id);
  }

  @Get('code/:code')
  @RequirePermission('permissions.read')
  @ApiOperation({ summary: 'Get a permission by code' })
  @ApiResponse({
    status: 200,
    description: 'Return the permission with the specified code',
    type: PermissionResponseDto,
  })
  findByCode(@Param('code') code: string): Promise<PermissionResponseDto> {
    return this.permissionService.findByCode(code);
  }

  @Patch(':id')
  @RequirePermission('permissions.update')
  @ApiOperation({ summary: 'Update a permission' })
  @ApiResponse({
    status: 200,
    description: 'The permission has been successfully updated.',
    type: PermissionResponseDto,
  })
  update(
    @Param('id', ParseIdPipe) id: number,
    @Body() updatePermissionDto: UpdatePermissionDto,
  ): Promise<PermissionResponseDto> {
    return this.permissionService.update(id, updatePermissionDto);
  }

  @Delete(':id')
  @RequirePermission('permissions.delete')
  @ApiOperation({ summary: 'Delete a permission' })
  @ApiResponse({
    status: 200,
    description: 'The permission has been successfully deleted.',
  })
  remove(@Param('id', ParseIdPipe) id: number): Promise<void> {
    return this.permissionService.remove(id);
  }
}
