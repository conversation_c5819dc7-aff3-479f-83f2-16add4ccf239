import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { PermissionDependencyService } from '../services/permission-dependency.service';
import { CreatePermissionDependencyDto } from '../dto/create-permission-dependency.dto';
import { PermissionDependencyResponseDto } from '../dto/permission-dependency-response.dto';
import { ParseIdPipe } from '@common/pipes/parse-id.pipe';
import { RequirePermission } from '../decorators/require-permission.decorator';
import { PERMISSION_PERMISSIONS } from '../constants';

@ApiTags('permission-dependencies')
@Controller('permission-dependencies')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PermissionDependencyController {
  constructor(
    private readonly permissionDependencyService: PermissionDependencyService,
  ) {}

  @Post()
  @RequirePermission(PERMISSION_PERMISSIONS.CREATE)
  @ApiOperation({ summary: 'Create a new permission dependency' })
  @ApiResponse({
    status: 201,
    description: 'The permission dependency has been successfully created.',
    type: PermissionDependencyResponseDto,
  })
  create(
    @Body() createPermissionDependencyDto: CreatePermissionDependencyDto,
  ): Promise<PermissionDependencyResponseDto> {
    return this.permissionDependencyService.create(createPermissionDependencyDto);
  }

  @Get()
  @RequirePermission(PERMISSION_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get all permission dependencies' })
  @ApiResponse({
    status: 200,
    description: 'Return all permission dependencies',
    type: [PermissionDependencyResponseDto],
  })
  findAll(): Promise<PermissionDependencyResponseDto[]> {
    return this.permissionDependencyService.findAll();
  }

  @Get('permission/:code')
  @RequirePermission(PERMISSION_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get permission dependencies by permission code' })
  @ApiResponse({
    status: 200,
    description: 'Return permission dependencies by permission code',
    type: [PermissionDependencyResponseDto],
  })
  findByPermissionCode(
    @Param('code') code: string,
  ): Promise<PermissionDependencyResponseDto[]> {
    return this.permissionDependencyService.findByPermissionCode(code);
  }

  @Get('dependent/:code')
  @RequirePermission(PERMISSION_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get permission dependencies by dependent permission code' })
  @ApiResponse({
    status: 200,
    description: 'Return permission dependencies by dependent permission code',
    type: [PermissionDependencyResponseDto],
  })
  findByDependentPermissionCode(
    @Param('code') code: string,
  ): Promise<PermissionDependencyResponseDto[]> {
    return this.permissionDependencyService.findByDependentPermissionCode(code);
  }

  @Delete(':id')
  @RequirePermission(PERMISSION_PERMISSIONS.DELETE)
  @ApiOperation({ summary: 'Delete a permission dependency' })
  @ApiResponse({
    status: 200,
    description: 'The permission dependency has been successfully deleted.',
  })
  remove(@Param('id', ParseIdPipe) id: number): Promise<void> {
    return this.permissionDependencyService.remove(id);
  }
}
