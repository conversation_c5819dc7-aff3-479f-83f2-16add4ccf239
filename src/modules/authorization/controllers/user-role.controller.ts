import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { UserRoleService } from '../services/user-role.service';
import {
  AssignRolesDto,
  RoleResponseDto,
  PermissionResponseDto,
  CheckPermissionDto,
} from '../dto';
import { ParseIdPipe } from '@common/pipes/parse-id.pipe';
import { RequirePermission } from '../decorators/require-permission.decorator';

@ApiTags('user-roles')
@Controller('user-roles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserRoleController {
  constructor(private readonly userRoleService: UserRoleService) {}

  @Post('users/:userId/roles')
  @RequirePermission('user-roles.assign')
  @ApiOperation({ summary: 'Assign roles to a user' })
  @ApiResponse({
    status: 200,
    description: 'The roles have been successfully assigned to the user.',
  })
  assignRolesToUser(
    @Param('userId', ParseIdPipe) userId: number,
    @Body() assignRolesDto: AssignRolesDto,
  ): Promise<boolean> {
    return this.userRoleService.assignRolesToUser(
      userId,
      assignRolesDto.roleIds,
    );
  }

  @Get('users/:userId/roles')
  @RequirePermission('user-roles.read')
  @ApiOperation({ summary: 'Get roles for a user' })
  @ApiResponse({
    status: 200,
    description: 'Return the roles for the user with the specified ID',
    type: [RoleResponseDto],
  })
  getRolesForUser(
    @Param('userId', ParseIdPipe) userId: number,
  ): Promise<RoleResponseDto[]> {
    return this.userRoleService.getRolesForUser(userId);
  }

  @Get('users/:userId/permissions')
  @RequirePermission('user-roles.read')
  @ApiOperation({ summary: 'Get permissions for a user' })
  @ApiResponse({
    status: 200,
    description: 'Return the permissions for the user with the specified ID',
    type: [PermissionResponseDto],
  })
  getPermissionsForUser(
    @Param('userId', ParseIdPipe) userId: number,
  ): Promise<PermissionResponseDto[]> {
    return this.userRoleService.getPermissionsForUser(userId);
  }

  @Post('users/:userId/check-permission')
  @RequirePermission('user-roles.read')
  @ApiOperation({ summary: 'Check if a user has a permission' })
  @ApiResponse({
    status: 200,
    description: 'Return whether the user has the specified permission',
    schema: {
      type: 'object',
      properties: {
        hasPermission: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  hasPermission(
    @Param('userId', ParseIdPipe) userId: number,
    @Body() checkPermissionDto: CheckPermissionDto,
  ): Promise<{ hasPermission: boolean }> {
    return this.userRoleService
      .hasPermission(userId, checkPermissionDto.permissionCode)
      .then((hasPermission) => ({ hasPermission }));
  }

  @Delete('users/:userId/roles')
  @RequirePermission('user-roles.delete')
  @ApiOperation({ summary: 'Remove all roles from a user' })
  @ApiResponse({
    status: 200,
    description: 'All roles have been successfully removed from the user.',
  })
  removeAllRolesFromUser(
    @Param('userId', ParseIdPipe) userId: number,
  ): Promise<boolean> {
    return this.userRoleService.removeAllRolesFromUser(userId);
  }
}
