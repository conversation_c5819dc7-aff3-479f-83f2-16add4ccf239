import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { PermissionGroupService } from '../services/permission-group.service';
import {
  CreatePermissionGroupDto,
  UpdatePermissionGroupDto,
  PermissionGroupResponseDto,
} from '../dto';
import { ParseIdPipe } from '@common/pipes/parse-id.pipe';
import { RequirePermission } from '../decorators/require-permission.decorator';
import { PERMISSION_GROUP_PERMISSIONS } from '../constants';

@ApiTags('permission-groups')
@Controller('permission-groups')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PermissionGroupController {
  constructor(
    private readonly permissionGroupService: PermissionGroupService,
  ) {}

  @Post()
  @RequirePermission(PERMISSION_GROUP_PERMISSIONS.CREATE)
  @ApiOperation({ summary: 'Create a new permission group' })
  @ApiResponse({
    status: 201,
    description: 'The permission group has been successfully created.',
    type: PermissionGroupResponseDto,
  })
  create(
    @Body() createPermissionGroupDto: CreatePermissionGroupDto,
  ): Promise<PermissionGroupResponseDto> {
    return this.permissionGroupService.create(createPermissionGroupDto);
  }

  @Get()
  @RequirePermission(PERMISSION_GROUP_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get all permission groups' })
  @ApiResponse({
    status: 200,
    description: 'Return all permission groups',
    type: [PermissionGroupResponseDto],
  })
  findAll(): Promise<PermissionGroupResponseDto[]> {
    return this.permissionGroupService.findAll();
  }

  @Get('with-permissions')
  @RequirePermission(PERMISSION_GROUP_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get all permission groups with their permissions' })
  @ApiResponse({
    status: 200,
    description: 'Return all permission groups with their permissions',
    type: [PermissionGroupResponseDto],
  })
  findAllWithPermissions(): Promise<PermissionGroupResponseDto[]> {
    return this.permissionGroupService.findAllWithPermissions();
  }

  @Get(':id')
  @RequirePermission(PERMISSION_GROUP_PERMISSIONS.READ)
  @ApiOperation({ summary: 'Get a permission group by ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the permission group with the specified ID',
    type: PermissionGroupResponseDto,
  })
  findOne(
    @Param('id', ParseIdPipe) id: number,
  ): Promise<PermissionGroupResponseDto> {
    return this.permissionGroupService.findOne(id);
  }

  @Patch(':id')
  @RequirePermission(PERMISSION_GROUP_PERMISSIONS.UPDATE)
  @ApiOperation({ summary: 'Update a permission group' })
  @ApiResponse({
    status: 200,
    description: 'The permission group has been successfully updated.',
    type: PermissionGroupResponseDto,
  })
  update(
    @Param('id', ParseIdPipe) id: number,
    @Body() updatePermissionGroupDto: UpdatePermissionGroupDto,
  ): Promise<PermissionGroupResponseDto> {
    return this.permissionGroupService.update(id, updatePermissionGroupDto);
  }

  @Delete(':id')
  @RequirePermission(PERMISSION_GROUP_PERMISSIONS.DELETE)
  @ApiOperation({ summary: 'Delete a permission group' })
  @ApiResponse({
    status: 200,
    description: 'The permission group has been successfully deleted.',
  })
  remove(@Param('id', ParseIdPipe) id: number): Promise<void> {
    return this.permissionGroupService.remove(id);
  }
}
