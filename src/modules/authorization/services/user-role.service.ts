import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  IUserRoleRepository,
  USER_ROLE_REPOSITORY,
} from '../repositories/user-role.repository.interface';
import { UsersService } from '@modules/users/services/users.service';
import { RoleService } from './role.service';
import { RoleResponseDto, PermissionResponseDto } from '../dto';

@Injectable()
export class UserRoleService {
  constructor(
    @Inject(USER_ROLE_REPOSITORY)
    private readonly userRoleRepository: IUserRoleRepository,
    private readonly usersService: UsersService,
    private readonly roleService: RoleService,
  ) {}

  async assignRolesToUser(
    userId: number,
    roleIds: number[],
  ): Promise<boolean> {
    // Check if user exists
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Check if all roles exist
    for (const roleId of roleIds) {
      await this.roleService.findOne(roleId);
    }

    return this.userRoleRepository.assignRolesToUser(userId, roleIds);
  }

  async getRolesForUser(userId: number): Promise<RoleResponseDto[]> {
    // Check if user exists
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const roles = await this.userRoleRepository.getRolesForUser(userId);
    return roles.map((role) => new RoleResponseDto(role));
  }

  async getPermissionsForUser(userId: number): Promise<PermissionResponseDto[]> {
    // Check if user exists
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const permissions = await this.userRoleRepository.getPermissionsForUser(userId);
    return permissions.map(
      (permission) => new PermissionResponseDto(permission),
    );
  }

  async hasPermission(userId: number, permissionCode: string): Promise<boolean> {
    return this.userRoleRepository.hasPermission(userId, permissionCode);
  }

  async removeAllRolesFromUser(userId: number): Promise<boolean> {
    // Check if user exists
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return this.userRoleRepository.removeAllRolesFromUser(userId);
  }
}
