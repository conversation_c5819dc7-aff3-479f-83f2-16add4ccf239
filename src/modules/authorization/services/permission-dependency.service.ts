import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  IPermissionDependencyRepository,
  PERMISSION_DEPENDENCY_REPOSITORY,
} from '../repositories/permission-dependency.repository.interface';
import {
  IPermissionRepository,
  PERMISSION_REPOSITORY,
} from '../repositories/permission.repository.interface';
import { PermissionDependency } from '../entities/permission-dependency.entity';
import { CreatePermissionDependencyDto } from '../dto/create-permission-dependency.dto';
import { PermissionDependencyResponseDto } from '../dto/permission-dependency-response.dto';

@Injectable()
export class PermissionDependencyService {
  constructor(
    @Inject(PERMISSION_DEPENDENCY_REPOSITORY)
    private readonly permissionDependencyRepository: IPermissionDependencyRepository,
    @Inject(PERMISSION_REPOSITORY)
    private readonly permissionRepository: IPermissionRepository,
  ) {}

  /**
   * Create a permission dependency
   * @param createPermissionDependencyDto Create permission dependency DTO
   */
  async create(
    createPermissionDependencyDto: CreatePermissionDependencyDto,
  ): Promise<PermissionDependencyResponseDto> {
    const { permissionCode, dependentPermissionCode } = createPermissionDependencyDto;

    // Find permission by code
    const permission = await this.permissionRepository.findByCode(permissionCode);
    if (!permission) {
      throw new NotFoundException(`Permission with code ${permissionCode} not found`);
    }

    // Find dependent permission by code
    const dependentPermission = await this.permissionRepository.findByCode(
      dependentPermissionCode,
    );
    if (!dependentPermission) {
      throw new NotFoundException(
        `Dependent permission with code ${dependentPermissionCode} not found`,
      );
    }

    // Create permission dependency
    const permissionDependency = await this.permissionDependencyRepository.create(
      permission.id,
      dependentPermission.id,
    );

    return this.toResponseDto(permissionDependency);
  }

  /**
   * Find all permission dependencies
   */
  async findAll(): Promise<PermissionDependencyResponseDto[]> {
    const permissionDependencies = await this.permissionDependencyRepository.findAll();
    return permissionDependencies.map((permissionDependency) =>
      this.toResponseDto(permissionDependency),
    );
  }

  /**
   * Find permission dependencies by permission code
   * @param permissionCode Permission code
   */
  async findByPermissionCode(
    permissionCode: string,
  ): Promise<PermissionDependencyResponseDto[]> {
    const permissionDependencies =
      await this.permissionDependencyRepository.findByPermissionCode(permissionCode);
    return permissionDependencies.map((permissionDependency) =>
      this.toResponseDto(permissionDependency),
    );
  }

  /**
   * Find permission dependencies by dependent permission code
   * @param dependentPermissionCode Dependent permission code
   */
  async findByDependentPermissionCode(
    dependentPermissionCode: string,
  ): Promise<PermissionDependencyResponseDto[]> {
    const permissionDependencies =
      await this.permissionDependencyRepository.findByDependentPermissionCode(
        dependentPermissionCode,
      );
    return permissionDependencies.map((permissionDependency) =>
      this.toResponseDto(permissionDependency),
    );
  }

  /**
   * Delete a permission dependency
   * @param id Permission dependency ID
   */
  async remove(id: number): Promise<void> {
    await this.permissionDependencyRepository.delete(id);
  }

  /**
   * Get all dependent permission codes for a permission code
   * This includes direct and indirect dependencies (transitive closure)
   * @param permissionCode Permission code
   */
  async getAllDependentPermissionCodes(permissionCode: string): Promise<string[]> {
    const visited = new Set<string>();
    const result: string[] = [];

    await this.getDependentPermissionCodesRecursive(permissionCode, visited, result);

    return result;
  }

  /**
   * Helper method to recursively get dependent permission codes
   * @param permissionCode Permission code
   * @param visited Set of visited permission codes
   * @param result Array of dependent permission codes
   */
  private async getDependentPermissionCodesRecursive(
    permissionCode: string,
    visited: Set<string>,
    result: string[],
  ): Promise<void> {
    if (visited.has(permissionCode)) {
      return;
    }

    visited.add(permissionCode);

    const dependencies = await this.permissionDependencyRepository.findByPermissionCode(
      permissionCode,
    );

    for (const dependency of dependencies) {
      const dependentPermissionCode = dependency.dependentPermission.code;
      result.push(dependentPermissionCode);
      await this.getDependentPermissionCodesRecursive(
        dependentPermissionCode,
        visited,
        result,
      );
    }
  }

  /**
   * Convert permission dependency entity to response DTO
   * @param permissionDependency Permission dependency entity
   */
  private toResponseDto(
    permissionDependency: PermissionDependency,
  ): PermissionDependencyResponseDto {
    return {
      id: permissionDependency.id,
      permissionId: permissionDependency.permissionId,
      dependentPermissionId: permissionDependency.dependentPermissionId,
      permissionCode: permissionDependency.permission?.code,
      dependentPermissionCode: permissionDependency.dependentPermission?.code,
      createdAt: permissionDependency.createdAt,
      updatedAt: permissionDependency.updatedAt,
    };
  }
}
