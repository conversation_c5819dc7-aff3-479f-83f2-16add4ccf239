import {
  Inject,
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import {
  IPermissionRepository,
  PERMISSION_REPOSITORY,
} from '../repositories/permission.repository.interface';
import { Permission } from '../entities/permission.entity';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionResponseDto,
} from '../dto';

@Injectable()
export class PermissionService {
  constructor(
    @Inject(PERMISSION_REPOSITORY)
    private readonly permissionRepository: IPermissionRepository,
  ) {}

  async create(
    createPermissionDto: CreatePermissionDto,
  ): Promise<PermissionResponseDto> {
    // Check if permission with this code already exists
    const existingPermission = await this.permissionRepository.findByCode(
      createPermissionDto.code,
    );
    if (existingPermission) {
      throw new ConflictException(
        `Permission with code ${createPermissionDto.code} already exists`,
      );
    }

    const permission = await this.permissionRepository.create(
      createPermissionDto,
    );
    return new PermissionResponseDto(permission);
  }

  async findAll(): Promise<PermissionResponseDto[]> {
    const permissions = await this.permissionRepository.findAll();
    return permissions.map((permission) => new PermissionResponseDto(permission));
  }

  async findAllWithGroups(): Promise<PermissionResponseDto[]> {
    const permissions = await this.permissionRepository.findAllWithGroups();
    return permissions.map((permission) => new PermissionResponseDto(permission));
  }

  async findPublicPermissions(): Promise<PermissionResponseDto[]> {
    const permissions = await this.permissionRepository.findPublicPermissions();
    return permissions.map((permission) => new PermissionResponseDto(permission));
  }

  async findOne(id: number): Promise<PermissionResponseDto> {
    const permission = await this.permissionRepository.findOneById(id);
    if (!permission) {
      throw new NotFoundException(`Permission with ID ${id} not found`);
    }
    return new PermissionResponseDto(permission);
  }

  async findByCode(code: string): Promise<PermissionResponseDto> {
    const permission = await this.permissionRepository.findByCode(code);
    if (!permission) {
      throw new NotFoundException(`Permission with code ${code} not found`);
    }
    return new PermissionResponseDto(permission);
  }

  async update(
    id: number,
    updatePermissionDto: UpdatePermissionDto,
  ): Promise<PermissionResponseDto> {
    // If code is being updated, check if it already exists
    if (updatePermissionDto.code) {
      const existingPermission = await this.permissionRepository.findByCode(
        updatePermissionDto.code,
      );
      if (existingPermission && existingPermission.id !== id) {
        throw new ConflictException(
          `Permission with code ${updatePermissionDto.code} already exists`,
        );
      }
    }

    const permission = await this.permissionRepository.update(
      id,
      updatePermissionDto,
    );
    return new PermissionResponseDto(permission);
  }

  async remove(id: number): Promise<void> {
    return this.permissionRepository.remove(id);
  }
}
