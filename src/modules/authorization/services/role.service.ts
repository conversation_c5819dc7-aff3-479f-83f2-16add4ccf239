import {
  Inject,
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import {
  IRoleRepository,
  ROLE_REPOSITORY,
} from '../repositories/role.repository.interface';
import { Role } from '../entities/role.entity';
import {
  CreateRoleDto,
  UpdateRoleDto,
  RoleResponseDto,
  PermissionResponseDto,
} from '../dto';

@Injectable()
export class RoleService {
  constructor(
    @Inject(ROLE_REPOSITORY)
    private readonly roleRepository: IRoleRepository,
  ) {}

  async create(createRoleDto: CreateRoleDto): Promise<RoleResponseDto> {
    // Check if role with this name already exists
    const existingRole = await this.roleRepository.findByName(
      createRoleDto.name,
    );
    if (existingRole) {
      throw new ConflictException(
        `Role with name ${createRoleDto.name} already exists`,
      );
    }

    // If this is set as default, unset any existing default role
    if (createRoleDto.isDefault) {
      await this.unsetDefaultRole();
    }

    // Extract permission IDs
    const { permissionIds, ...roleData } = createRoleDto;

    // Create role
    const role = await this.roleRepository.create(roleData);

    // Assign permissions if provided
    if (permissionIds && permissionIds.length > 0) {
      await this.roleRepository.assignPermissions(role.id, permissionIds);
    }

    // Return role with permissions
    return this.findOne(role.id);
  }

  async findAll(): Promise<RoleResponseDto[]> {
    const roles = await this.roleRepository.findAll();
    return roles.map((role) => new RoleResponseDto(role));
  }

  async findAllWithPermissions(): Promise<RoleResponseDto[]> {
    const roles = await this.roleRepository.findAllWithPermissions();
    return roles.map((role) => new RoleResponseDto(role));
  }

  async findOne(id: number): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findOneWithPermissions(id);
    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }
    return new RoleResponseDto(role);
  }

  async update(
    id: number,
    updateRoleDto: UpdateRoleDto,
  ): Promise<RoleResponseDto> {
    // Get the role to check if it's a system role
    const role = await this.roleRepository.findOneById(id);
    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    if (role.isSystem) {
      throw new ConflictException(`System roles cannot be modified`);
    }

    // If name is being updated, check if it already exists
    if (updateRoleDto.name) {
      const existingRole = await this.roleRepository.findByName(
        updateRoleDto.name,
      );
      if (existingRole && existingRole.id !== id) {
        throw new ConflictException(
          `Role with name ${updateRoleDto.name} already exists`,
        );
      }
    }

    // If this is set as default, unset any existing default role
    if (updateRoleDto.isDefault) {
      await this.unsetDefaultRole();
    }

    // Extract permission IDs
    const { permissionIds, ...roleData } = updateRoleDto;

    // Update role
    await this.roleRepository.update(id, roleData);

    // Assign permissions if provided
    if (permissionIds !== undefined) {
      await this.roleRepository.assignPermissions(id, permissionIds || []);
    }

    // Return updated role with permissions
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    // Get the role to check if it's a system role
    const role = await this.roleRepository.findOneById(id);
    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    if (role.isSystem) {
      throw new ConflictException(`System roles cannot be deleted`);
    }

    return this.roleRepository.remove(id);
  }

  async getPermissions(roleId: number): Promise<PermissionResponseDto[]> {
    const role = await this.roleRepository.findOneById(roleId);
    if (!role) {
      throw new NotFoundException(`Role with ID ${roleId} not found`);
    }

    const permissions = await this.roleRepository.getPermissions(roleId);
    return permissions.map(
      (permission) => new PermissionResponseDto(permission),
    );
  }

  async assignPermissions(
    roleId: number,
    permissionIds: number[],
  ): Promise<boolean> {
    const role = await this.roleRepository.findOneById(roleId);
    if (!role) {
      throw new NotFoundException(`Role with ID ${roleId} not found`);
    }

    if (role.isSystem) {
      throw new ConflictException(`System roles cannot be modified`);
    }

    return this.roleRepository.assignPermissions(roleId, permissionIds);
  }

  private async unsetDefaultRole(): Promise<void> {
    const defaultRole = await this.roleRepository.findDefaultRole();
    if (defaultRole) {
      await this.roleRepository.update(defaultRole.id, { isDefault: false });
    }
  }
}
