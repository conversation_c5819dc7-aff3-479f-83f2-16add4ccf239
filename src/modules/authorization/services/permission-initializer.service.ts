import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { AuthorizationSeeder } from '../authorization.seeder';

/**
 * Service to initialize permissions when the application starts
 */
@Injectable()
export class PermissionInitializerService implements OnModuleInit {
  private readonly logger = new Logger(PermissionInitializerService.name);

  constructor(private readonly authorizationSeeder: AuthorizationSeeder) {}

  /**
   * Initialize permissions when the module is initialized
   */
  async onModuleInit() {
    this.logger.log('Initializing permissions...');
    try {
      await this.authorizationSeeder.seed();
      this.logger.log('Permissions initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize permissions', error);
    }
  }
}
