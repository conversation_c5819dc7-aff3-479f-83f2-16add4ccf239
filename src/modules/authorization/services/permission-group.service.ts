import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  IPermissionGroupRepository,
  PERMISSION_GROUP_REPOSITORY,
} from '../repositories/permission-group.repository.interface';
import { PermissionGroup } from '../entities/permission-group.entity';
import {
  CreatePermissionGroupDto,
  UpdatePermissionGroupDto,
  PermissionGroupResponseDto,
} from '../dto';

@Injectable()
export class PermissionGroupService {
  constructor(
    @Inject(PERMISSION_GROUP_REPOSITORY)
    private readonly permissionGroupRepository: IPermissionGroupRepository,
  ) {}

  async create(
    createPermissionGroupDto: CreatePermissionGroupDto,
  ): Promise<PermissionGroupResponseDto> {
    const permissionGroup = await this.permissionGroupRepository.create(
      createPermissionGroupDto,
    );
    return new PermissionGroupResponseDto(permissionGroup);
  }

  async findAll(): Promise<PermissionGroupResponseDto[]> {
    const permissionGroups = await this.permissionGroupRepository.findAll();
    return permissionGroups.map(
      (group) => new PermissionGroupResponseDto(group),
    );
  }

  async findAllWithPermissions(): Promise<PermissionGroupResponseDto[]> {
    const permissionGroups =
      await this.permissionGroupRepository.findAllWithPermissions();
    return permissionGroups.map(
      (group) => new PermissionGroupResponseDto(group),
    );
  }

  async findOne(id: number): Promise<PermissionGroupResponseDto> {
    const permissionGroup = await this.permissionGroupRepository.findOneById(id);
    if (!permissionGroup) {
      throw new NotFoundException(`Permission group with ID ${id} not found`);
    }
    return new PermissionGroupResponseDto(permissionGroup);
  }

  async update(
    id: number,
    updatePermissionGroupDto: UpdatePermissionGroupDto,
  ): Promise<PermissionGroupResponseDto> {
    const permissionGroup = await this.permissionGroupRepository.update(
      id,
      updatePermissionGroupDto,
    );
    return new PermissionGroupResponseDto(permissionGroup);
  }

  async remove(id: number): Promise<void> {
    return this.permissionGroupRepository.remove(id);
  }
}
