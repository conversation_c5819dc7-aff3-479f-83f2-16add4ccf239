import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '@core/database/base/base.entity';
import { Permission } from './permission.entity';

/**
 * Permission dependency entity
 * This entity represents a dependency between permissions
 * When a permission is granted, all its dependent permissions are also granted
 */
@Entity('permission_dependencies')
export class PermissionDependency extends BaseEntity {
  @Column({ name: 'permission_id' })
  permissionId: number;

  @Column({ name: 'dependent_permission_id' })
  dependentPermissionId: number;

  @ManyToOne(() => Permission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;

  @ManyToOne(() => Permission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'dependent_permission_id' })
  dependentPermission: Permission;
}
