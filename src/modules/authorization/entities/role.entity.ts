import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>any } from 'typeorm';
import { BaseEntity } from '@core/database/base/base.entity';
import { RolePermission } from './role-permission.entity';

/**
 * Role entity
 * This entity represents a role in the system
 */
@Entity('roles')
export class Role extends BaseEntity {
  @Column({ unique: true })
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ default: false })
  isDefault: boolean;

  @Column({ default: false })
  isSystem: boolean;

  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.role)
  rolePermissions: RolePermission[];
}
