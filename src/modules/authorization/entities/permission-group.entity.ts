import { <PERSON>umn, <PERSON>ti<PERSON>, <PERSON>ToMany } from 'typeorm';
import { BaseEntity } from '@core/database/base/base.entity';
import { Permission } from './permission.entity';

/**
 * Permission Group entity
 * This entity represents a group of permissions in the system
 */
@Entity('permission_groups')
export class PermissionGroup extends BaseEntity {
  @Column({ unique: true })
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ default: 0 })
  displayOrder: number;

  @OneToMany(() => Permission, (permission) => permission.group)
  permissions: Permission[];
}
