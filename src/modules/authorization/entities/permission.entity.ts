import { Column, <PERSON>tity, ManyToOne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '@core/database/base/base.entity';
import { PermissionGroup } from './permission-group.entity';
import { RolePermission } from './role-permission.entity';

/**
 * Permission entity
 * This entity represents a permission in the system
 */
@Entity('permissions')
export class Permission extends BaseEntity {
  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ default: 0 })
  displayOrder: number;

  @Column({ default: false })
  isPublic: boolean;

  @ManyToOne(() => PermissionGroup, (group) => group.permissions, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'group_id' })
  group: PermissionGroup;

  @Column({ nullable: true })
  groupId: number;

  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.permission)
  rolePermissions: RolePermission[];
}
