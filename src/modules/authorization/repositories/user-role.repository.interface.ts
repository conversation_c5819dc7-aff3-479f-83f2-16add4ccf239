import { UserRole } from '../entities/user-role.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';

/**
 * User Role repository interface
 */
export interface IUserRoleRepository {
  /**
   * Assign roles to a user
   * @param userId User ID
   * @param roleIds Role IDs
   * @returns Promise with success status
   */
  assignRolesToUser(userId: number, roleIds: number[]): Promise<boolean>;

  /**
   * Get roles for a user
   * @param userId User ID
   * @returns Promise with roles
   */
  getRolesForUser(userId: number): Promise<Role[]>;

  /**
   * Get permissions for a user
   * @param userId User ID
   * @returns Promise with permissions
   */
  getPermissionsForUser(userId: number): Promise<Permission[]>;

  /**
   * Check if a user has a permission
   * @param userId User ID
   * @param permissionCode Permission code
   * @returns Promise with boolean indicating if user has permission
   */
  hasPermission(userId: number, permissionCode: string): Promise<boolean>;

  /**
   * Check if a user has a permission, including implied permissions
   * @param userId User ID
   * @param permissionCode Permission code
   * @returns Promise with boolean indicating if user has permission
   */
  hasPermissionWithImplied(
    userId: number,
    permissionCode: string,
  ): Promise<boolean>;

  /**
   * Remove all roles from a user
   * @param userId User ID
   * @returns Promise with success status
   */
  removeAllRolesFromUser(userId: number): Promise<boolean>;
}

/**
 * User Role repository token for dependency injection
 */
export const USER_ROLE_REPOSITORY = 'USER_ROLE_REPOSITORY';
