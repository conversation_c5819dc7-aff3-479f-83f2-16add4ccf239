import { IBaseRepository } from '@core/database/base/base.repository.interface';
import { PermissionGroup } from '../entities/permission-group.entity';

/**
 * Permission Group repository interface
 */
export interface IPermissionGroupRepository extends IBaseRepository<PermissionGroup> {
  /**
   * Find all permission groups with their permissions
   * @returns Promise with permission groups and their permissions
   */
  findAllWithPermissions(): Promise<PermissionGroup[]>;
}

/**
 * Permission Group repository token for dependency injection
 */
export const PERMISSION_GROUP_REPOSITORY = 'PERMISSION_GROUP_REPOSITORY';
