import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BaseRepository } from '@core/database/base/base.repository';
import { Permission } from '../entities/permission.entity';
import { IPermissionRepository } from './permission.repository.interface';
import { DATABASE_CONNECTION } from '@core/database/database.providers';

@Injectable()
export class PermissionRepository
  extends BaseRepository<Permission>
  implements IPermissionRepository
{
  private readonly permissionRepository: Repository<Permission>;

  constructor(
    @Inject(DATABASE_CONNECTION)
    dataSource: DataSource,
  ) {
    const permissionRepository = dataSource.getRepository(Permission);
    super(permissionRepository);
    this.permissionRepository = permissionRepository;
  }

  async findByCode(code: string): Promise<Permission | null> {
    return this.permissionRepository.findOne({
      where: { code },
    });
  }

  async findAllWithGroups(): Promise<Permission[]> {
    return this.permissionRepository.find({
      relations: ['group'],
      order: {
        group: {
          displayOrder: 'ASC',
          name: 'ASC',
        },
        displayOrder: 'ASC',
        name: 'ASC',
      },
    });
  }

  async findPublicPermissions(): Promise<Permission[]> {
    return this.permissionRepository.find({
      where: { isPublic: true },
      relations: ['group'],
      order: {
        group: {
          displayOrder: 'ASC',
          name: 'ASC',
        },
        displayOrder: 'ASC',
        name: 'ASC',
      },
    });
  }
}
