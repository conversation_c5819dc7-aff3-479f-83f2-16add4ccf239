import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository, In } from 'typeorm';
import { BaseRepository } from '@core/database/base/base.repository';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { RolePermission } from '../entities/role-permission.entity';
import { IRoleRepository } from './role.repository.interface';
import { DATABASE_CONNECTION } from '@core/database/database.providers';

@Injectable()
export class RoleRepository
  extends BaseRepository<Role>
  implements IRoleRepository
{
  private readonly roleRepository: Repository<Role>;
  private readonly rolePermissionRepository: Repository<RolePermission>;
  private readonly permissionRepository: Repository<Permission>;

  constructor(
    @Inject(DATABASE_CONNECTION)
    dataSource: DataSource,
  ) {
    const roleRepository = dataSource.getRepository(Role);
    super(roleRepository);
    this.roleRepository = roleRepository;
    this.rolePermissionRepository = dataSource.getRepository(RolePermission);
    this.permissionRepository = dataSource.getRepository(Permission);
  }

  async findByName(name: string): Promise<Role | null> {
    return this.roleRepository.findOne({
      where: { name },
    });
  }

  async findAllWithPermissions(): Promise<Role[]> {
    return this.roleRepository.find({
      relations: ['rolePermissions', 'rolePermissions.permission', 'rolePermissions.permission.group'],
    });
  }

  async findOneWithPermissions(id: number): Promise<Role | null> {
    return this.roleRepository.findOne({
      where: { id },
      relations: ['rolePermissions', 'rolePermissions.permission', 'rolePermissions.permission.group'],
    });
  }

  async findDefaultRole(): Promise<Role | null> {
    return this.roleRepository.findOne({
      where: { isDefault: true },
    });
  }

  async assignPermissions(roleId: number, permissionIds: number[]): Promise<boolean> {
    // First, remove all existing permissions for this role
    await this.rolePermissionRepository.delete({ roleId });

    // Then, add the new permissions
    if (permissionIds.length > 0) {
      const rolePermissions = permissionIds.map(permissionId => ({
        roleId,
        permissionId,
      }));

      await this.rolePermissionRepository.insert(rolePermissions);
    }

    return true;
  }

  async getPermissions(roleId: number): Promise<Permission[]> {
    const rolePermissions = await this.rolePermissionRepository.find({
      where: { roleId },
      relations: ['permission', 'permission.group'],
    });

    return rolePermissions.map(rp => rp.permission);
  }
}
