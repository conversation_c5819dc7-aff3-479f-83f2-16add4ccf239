import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository, In } from 'typeorm';
import { UserRole } from '../entities/user-role.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { RolePermission } from '../entities/role-permission.entity';
import { PermissionDependency } from '../entities/permission-dependency.entity';
import { IUserRoleRepository } from './user-role.repository.interface';
import { DATABASE_CONNECTION } from '@core/database/database.providers';

@Injectable()
export class UserRoleRepository implements IUserRoleRepository {
  private readonly userRoleRepository: Repository<UserRole>;
  private readonly roleRepository: Repository<Role>;
  private readonly rolePermissionRepository: Repository<RolePermission>;
  private readonly permissionRepository: Repository<Permission>;
  private readonly permissionDependencyRepository: Repository<PermissionDependency>;

  constructor(
    @Inject(DATABASE_CONNECTION)
    dataSource: DataSource,
  ) {
    this.userRoleRepository = dataSource.getRepository(UserRole);
    this.roleRepository = dataSource.getRepository(Role);
    this.rolePermissionRepository = dataSource.getRepository(RolePermission);
    this.permissionRepository = dataSource.getRepository(Permission);
    this.permissionDependencyRepository =
      dataSource.getRepository(PermissionDependency);
  }

  async assignRolesToUser(userId: number, roleIds: number[]): Promise<boolean> {
    // First, remove all existing roles for this user
    await this.userRoleRepository.delete({ userId });

    // Then, add the new roles
    if (roleIds.length > 0) {
      const userRoles = roleIds.map((roleId) => ({
        userId,
        roleId,
      }));

      await this.userRoleRepository.insert(userRoles);
    }

    return true;
  }

  async getRolesForUser(userId: number): Promise<Role[]> {
    const userRoles = await this.userRoleRepository.find({
      where: { userId },
      relations: ['role'],
    });

    return userRoles.map((ur) => ur.role);
  }

  async getPermissionsForUser(userId: number): Promise<Permission[]> {
    // Get all roles for the user
    const userRoles = await this.userRoleRepository.find({
      where: { userId },
    });

    if (userRoles.length === 0) {
      return [];
    }

    const roleIds = userRoles.map((ur) => ur.roleId);

    // Get all permissions for these roles
    const rolePermissions = await this.rolePermissionRepository.find({
      where: { roleId: In(roleIds) },
      relations: ['permission', 'permission.group'],
    });

    // Extract unique permissions
    const permissionMap = new Map<number, Permission>();
    rolePermissions.forEach((rp) => {
      if (!permissionMap.has(rp.permission.id)) {
        permissionMap.set(rp.permission.id, rp.permission);
      }
    });

    return Array.from(permissionMap.values());
  }

  async hasPermission(
    userId: number,
    permissionCode: string,
  ): Promise<boolean> {
    // First, check if the permission is public
    const permission = await this.permissionRepository.findOne({
      where: { code: permissionCode },
    });

    if (!permission) {
      return false;
    }

    if (permission.isPublic) {
      return true;
    }

    // Get all roles for the user
    const userRoles = await this.userRoleRepository.find({
      where: { userId },
    });

    if (userRoles.length === 0) {
      return false;
    }

    const roleIds = userRoles.map((ur) => ur.roleId);

    // Check if any of the user's roles has this permission
    const rolePermission = await this.rolePermissionRepository.findOne({
      where: {
        roleId: In(roleIds),
        permissionId: permission.id,
      },
    });

    return !!rolePermission;
  }

  async hasPermissionWithImplied(
    userId: number,
    permissionCode: string,
  ): Promise<boolean> {
    // First check if the user has the permission directly
    const hasDirectPermission = await this.hasPermission(
      userId,
      permissionCode,
    );
    if (hasDirectPermission) {
      return true;
    }

    // If not, check if the user has any permission that implies this permission
    // Get all permissions for the user
    const userPermissions = await this.getPermissionsForUser(userId);
    if (userPermissions.length === 0) {
      return false;
    }

    // For each permission, check if it implies the requested permission
    for (const userPermission of userPermissions) {
      // Get all dependent permissions for this permission
      const dependencies = await this.permissionDependencyRepository.find({
        where: { permissionId: userPermission.id },
        relations: ['dependentPermission'],
      });

      // Check if any of the dependent permissions matches the requested permission
      for (const dependency of dependencies) {
        if (dependency.dependentPermission.code === permissionCode) {
          return true;
        }
      }
    }

    return false;
  }

  async removeAllRolesFromUser(userId: number): Promise<boolean> {
    await this.userRoleRepository.delete({ userId });
    return true;
  }
}
