import { PermissionDependency } from '../entities/permission-dependency.entity';

export const PERMISSION_DEPENDENCY_REPOSITORY = 'PERMISSION_DEPENDENCY_REPOSITORY';

export interface IPermissionDependencyRepository {
  /**
   * Find all permission dependencies
   */
  findAll(): Promise<PermissionDependency[]>;

  /**
   * Find permission dependencies by permission ID
   * @param permissionId Permission ID
   */
  findByPermissionId(permissionId: number): Promise<PermissionDependency[]>;

  /**
   * Find permission dependencies by dependent permission ID
   * @param dependentPermissionId Dependent permission ID
   */
  findByDependentPermissionId(
    dependentPermissionId: number,
  ): Promise<PermissionDependency[]>;

  /**
   * Find permission dependencies by permission code
   * @param permissionCode Permission code
   */
  findByPermissionCode(permissionCode: string): Promise<PermissionDependency[]>;

  /**
   * Find permission dependencies by dependent permission code
   * @param dependentPermissionCode Dependent permission code
   */
  findByDependentPermissionCode(
    dependentPermissionCode: string,
  ): Promise<PermissionDependency[]>;

  /**
   * Create a permission dependency
   * @param permissionId Permission ID
   * @param dependentPermissionId Dependent permission ID
   */
  create(
    permissionId: number,
    dependentPermissionId: number,
  ): Promise<PermissionDependency>;

  /**
   * Delete a permission dependency
   * @param id Permission dependency ID
   */
  delete(id: number): Promise<void>;

  /**
   * Delete permission dependencies by permission ID
   * @param permissionId Permission ID
   */
  deleteByPermissionId(permissionId: number): Promise<void>;

  /**
   * Delete permission dependencies by dependent permission ID
   * @param dependentPermissionId Dependent permission ID
   */
  deleteByDependentPermissionId(dependentPermissionId: number): Promise<void>;
}
