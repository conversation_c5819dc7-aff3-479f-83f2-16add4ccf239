import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BaseRepository } from '@core/database/base/base.repository';
import { PermissionGroup } from '../entities/permission-group.entity';
import { IPermissionGroupRepository } from './permission-group.repository.interface';
import { DATABASE_CONNECTION } from '@core/database/database.providers';

@Injectable()
export class PermissionGroupRepository
  extends BaseRepository<PermissionGroup>
  implements IPermissionGroupRepository
{
  private readonly permissionGroupRepository: Repository<PermissionGroup>;

  constructor(
    @Inject(DATABASE_CONNECTION)
    dataSource: DataSource,
  ) {
    const permissionGroupRepository = dataSource.getRepository(PermissionGroup);
    super(permissionGroupRepository);
    this.permissionGroupRepository = permissionGroupRepository;
  }

  async findAllWithPermissions(): Promise<PermissionGroup[]> {
    return this.permissionGroupRepository.find({
      relations: ['permissions'],
      order: {
        displayOrder: 'ASC',
        name: '<PERSON><PERSON>',
        permissions: {
          displayOrder: 'ASC',
          name: '<PERSON><PERSON>',
        },
      },
    });
  }
}
