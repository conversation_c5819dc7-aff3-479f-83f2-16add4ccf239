import { Inject, Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DATABASE_CONNECTION } from '@core/database/database.providers';
import { PermissionDependency } from '../entities/permission-dependency.entity';
import { IPermissionDependencyRepository } from './permission-dependency.repository.interface';
import { Permission } from '../entities/permission.entity';

@Injectable()
export class PermissionDependencyRepository
  implements IPermissionDependencyRepository
{
  private repository: Repository<PermissionDependency>;
  private permissionRepository: Repository<Permission>;

  constructor(
    @Inject(DATABASE_CONNECTION)
    private dataSource: DataSource,
  ) {
    this.repository = this.dataSource.getRepository(PermissionDependency);
    this.permissionRepository = this.dataSource.getRepository(Permission);
  }

  async findAll(): Promise<PermissionDependency[]> {
    return this.repository.find({
      relations: ['permission', 'dependentPermission'],
    });
  }

  async findByPermissionId(permissionId: number): Promise<PermissionDependency[]> {
    return this.repository.find({
      where: { permissionId },
      relations: ['permission', 'dependentPermission'],
    });
  }

  async findByDependentPermissionId(
    dependentPermissionId: number,
  ): Promise<PermissionDependency[]> {
    return this.repository.find({
      where: { dependentPermissionId },
      relations: ['permission', 'dependentPermission'],
    });
  }

  async findByPermissionCode(permissionCode: string): Promise<PermissionDependency[]> {
    const permission = await this.permissionRepository.findOne({
      where: { code: permissionCode },
    });

    if (!permission) {
      return [];
    }

    return this.findByPermissionId(permission.id);
  }

  async findByDependentPermissionCode(
    dependentPermissionCode: string,
  ): Promise<PermissionDependency[]> {
    const permission = await this.permissionRepository.findOne({
      where: { code: dependentPermissionCode },
    });

    if (!permission) {
      return [];
    }

    return this.findByDependentPermissionId(permission.id);
  }

  async create(
    permissionId: number,
    dependentPermissionId: number,
  ): Promise<PermissionDependency> {
    const permissionDependency = this.repository.create({
      permissionId,
      dependentPermissionId,
    });

    return this.repository.save(permissionDependency);
  }

  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  async deleteByPermissionId(permissionId: number): Promise<void> {
    await this.repository.delete({ permissionId });
  }

  async deleteByDependentPermissionId(dependentPermissionId: number): Promise<void> {
    await this.repository.delete({ dependentPermissionId });
  }
}
