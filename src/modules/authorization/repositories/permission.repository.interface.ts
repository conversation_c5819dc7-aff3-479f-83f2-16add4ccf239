import { IBaseRepository } from '@core/database/base/base.repository.interface';
import { Permission } from '../entities/permission.entity';

/**
 * Permission repository interface
 */
export interface IPermissionRepository extends IBaseRepository<Permission> {
  /**
   * Find a permission by code
   * @param code Permission code
   * @returns Promise with the found permission or null
   */
  findByCode(code: string): Promise<Permission | null>;

  /**
   * Find all permissions with their groups
   * @returns Promise with permissions and their groups
   */
  findAllWithGroups(): Promise<Permission[]>;

  /**
   * Find all public permissions
   * @returns Promise with public permissions
   */
  findPublicPermissions(): Promise<Permission[]>;
}

/**
 * Permission repository token for dependency injection
 */
export const PERMISSION_REPOSITORY = 'PERMISSION_REPOSITORY';
