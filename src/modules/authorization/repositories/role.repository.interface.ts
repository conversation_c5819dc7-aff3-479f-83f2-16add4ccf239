import { IBaseRepository } from '@core/database/base/base.repository.interface';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';

/**
 * Role repository interface
 */
export interface IRoleRepository extends IBaseRepository<Role> {
  /**
   * Find a role by name
   * @param name Role name
   * @returns Promise with the found role or null
   */
  findByName(name: string): Promise<Role | null>;

  /**
   * Find all roles with their permissions
   * @returns Promise with roles and their permissions
   */
  findAllWithPermissions(): Promise<Role[]>;

  /**
   * Find a role with its permissions by ID
   * @param id Role ID
   * @returns Promise with the role and its permissions or null
   */
  findOneWithPermissions(id: number): Promise<Role | null>;

  /**
   * Find the default role
   * @returns Promise with the default role or null
   */
  findDefaultRole(): Promise<Role | null>;

  /**
   * Assign permissions to a role
   * @param roleId Role ID
   * @param permissionIds Permission IDs
   * @returns Promise with success status
   */
  assignPermissions(roleId: number, permissionIds: number[]): Promise<boolean>;

  /**
   * Get permissions for a role
   * @param roleId Role ID
   * @returns Promise with permissions
   */
  getPermissions(roleId: number): Promise<Permission[]>;
}

/**
 * Role repository token for dependency injection
 */
export const ROLE_REPOSITORY = 'ROLE_REPOSITORY';
