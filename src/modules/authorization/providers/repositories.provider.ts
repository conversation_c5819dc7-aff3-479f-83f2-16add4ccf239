import { Provider } from '@nestjs/common';
import { PERMISSION_GROUP_REPOSITORY } from '../repositories/permission-group.repository.interface';
import { PermissionGroupRepository } from '../repositories/permission-group.repository';
import { PERMISSION_REPOSITORY } from '../repositories/permission.repository.interface';
import { PermissionRepository } from '../repositories/permission.repository';
import { ROLE_REPOSITORY } from '../repositories/role.repository.interface';
import { RoleRepository } from '../repositories/role.repository';
import { USER_ROLE_REPOSITORY } from '../repositories/user-role.repository.interface';
import { UserRoleRepository } from '../repositories/user-role.repository';
import { PERMISSION_DEPENDENCY_REPOSITORY } from '../repositories/permission-dependency.repository.interface';
import { PermissionDependencyRepository } from '../repositories/permission-dependency.repository';

export const repositoriesProviders: Provider[] = [
  {
    provide: PERMISSION_GROUP_REPOSITORY,
    useClass: PermissionGroupRepository,
  },
  {
    provide: PERMISSION_REPOSITORY,
    useClass: PermissionRepository,
  },
  {
    provide: ROLE_REPOSITORY,
    useClass: RoleRepository,
  },
  {
    provide: USER_ROLE_REPOSITORY,
    useClass: UserRoleRepository,
  },
  {
    provide: PERMISSION_DEPENDENCY_REPOSITORY,
    useClass: PermissionDependencyRepository,
  },
];
