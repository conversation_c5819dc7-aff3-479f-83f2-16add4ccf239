import { ROLES, USER_PERMISSIONS, ROL<PERSON>_PERMISSIONS, PERMISSION_PERMISSIONS, PERMISSION_GROUP_PERMISSIONS, PRODUCT_PERMISSIONS } from '../../constants';

export const adminRolePermissions = {
  role: ROLES.ADMIN,
  permissions: [
    // User permissions
    USER_PERMISSIONS.CREATE,
    USER_PERMISSIONS.READ,
    USER_PERMISSIONS.UPDATE,
    USER_PERMISSIONS.DELETE,
    USER_PERMISSIONS.ASSIGN_ROLES,
    USER_PERMISSIONS.READ_ROLES,
    USER_PERMISSIONS.DELETE_ROLES,

    // Role permissions
    ROLE_PERMISSIONS.CREATE,
    ROLE_PERMISSIONS.READ,
    ROLE_PERMISSIONS.UPDATE,
    ROLE_PERMISSIONS.DELETE,

    // Permission permissions
    PERMISSION_PERMISSIONS.CREATE,
    PERMISSION_PERMISSIONS.READ,
    PERMISSION_PERMISSIONS.UPDATE,
    <PERSON>ERMI<PERSON><PERSON>_PERMISSIONS.DELETE,

    // Permission Group permissions
    PERMISSION_GROUP_PERMISSIONS.CREATE,
    PERMISSION_GROUP_PERMISSIONS.READ,
    PERMISSION_GROUP_PERMISSIONS.UPDATE,
    PERMISSION_GROUP_PERMISSIONS.DELETE,

    // Product permissions
    PRODUCT_PERMISSIONS.CREATE,
    PRODUCT_PERMISSIONS.READ,
    PRODUCT_PERMISSIONS.UPDATE,
    PRODUCT_PERMISSIONS.DELETE,
  ],
};
