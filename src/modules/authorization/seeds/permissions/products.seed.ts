import { Permission } from '../../entities/permission.entity';
import { PRODUCT_PERMISSIONS } from '../../constants';

export const productPermissions: Partial<Permission>[] = [
  {
    code: PRODUCT_PERMISSIONS.CREATE,
    name: 'Create Product',
    description: 'Create a new product',
    displayOrder: 1,
    isPublic: false,
  },
  {
    code: PRODUCT_PERMISSIONS.READ,
    name: 'Read Product',
    description: 'Read product information',
    displayOrder: 2,
    isPublic: true, // Public permission
  },
  {
    code: PRODUCT_PERMISSIONS.UPDATE,
    name: 'Update Product',
    description: 'Update product information',
    displayOrder: 3,
    isPublic: false,
  },
  {
    code: PRODUCT_PERMISSIONS.DELETE,
    name: 'Delete Product',
    description: 'Delete a product',
    displayOrder: 4,
    isPublic: false,
  },
];
