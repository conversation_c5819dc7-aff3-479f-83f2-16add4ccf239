import { Permission } from '../../entities/permission.entity';
import { PERMISSION_GROUP_PERMISSIONS } from '../../constants';

export const permissionGroupPermissions: Partial<Permission>[] = [
  {
    code: PERMISSION_GROUP_PERMISSIONS.CREATE,
    name: 'Create Permission Group',
    description: 'Create a new permission group',
    displayOrder: 1,
    isPublic: false,
  },
  {
    code: PERMISSION_GROUP_PERMISSIONS.READ,
    name: 'Read Permission Group',
    description: 'Read permission group information',
    displayOrder: 2,
    isPublic: false,
  },
  {
    code: PERMISSION_GROUP_PERMISSIONS.UPDATE,
    name: 'Update Permission Group',
    description: 'Update permission group information',
    displayOrder: 3,
    isPublic: false,
  },
  {
    code: PERMISSION_GROUP_PERMISSIONS.DELETE,
    name: 'Delete Permission Group',
    description: 'Delete a permission group',
    displayOrder: 4,
    isPublic: false,
  },
];
