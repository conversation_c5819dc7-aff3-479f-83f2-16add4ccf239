import { Permission } from '../../entities/permission.entity';
import { USER_PERMISSIONS, PERMISSION_GROUPS } from '../../constants';

export const userPermissions: Partial<Permission>[] = [
  {
    code: USER_PERMISSIONS.CREATE,
    name: 'Create User',
    description: 'Create a new user',
    displayOrder: 1,
    isPublic: false,
  },
  {
    code: USER_PERMISSIONS.READ,
    name: 'Read User',
    description: 'Read user information',
    displayOrder: 2,
    isPublic: false,
  },
  {
    code: USER_PERMISSIONS.UPDATE,
    name: 'Update User',
    description: 'Update user information',
    displayOrder: 3,
    isPublic: false,
  },
  {
    code: USER_PERMISSIONS.DELETE,
    name: 'Delete User',
    description: 'Delete a user',
    displayOrder: 4,
    isPublic: false,
  },
  {
    code: USER_PERMISSIONS.ASSIGN_ROLES,
    name: 'Assign Roles to User',
    description: 'Assign roles to a user',
    displayOrder: 5,
    isPublic: false,
  },
  {
    code: USER_PERMISSIONS.READ_ROLES,
    name: 'Read User Roles',
    description: 'Read roles assigned to a user',
    displayOrder: 6,
    isPublic: false,
  },
  {
    code: USER_PERMISSIONS.DELETE_ROLES,
    name: 'Delete User Roles',
    description: 'Remove roles from a user',
    displayOrder: 7,
    isPublic: false,
  },
];
