import { Permission } from '../../entities/permission.entity';
import { PERMISSION_PERMISSIONS } from '../../constants';

export const permissionPermissions: Partial<Permission>[] = [
  {
    code: PERMISSION_PERMISSIONS.CREATE,
    name: 'Create Permission',
    description: 'Create a new permission',
    displayOrder: 1,
    isPublic: false,
  },
  {
    code: PERMISSION_PERMISSIONS.READ,
    name: 'Read Permission',
    description: 'Read permission information',
    displayOrder: 2,
    isPublic: false,
  },
  {
    code: PERMISSION_PERMISSIONS.UPDATE,
    name: 'Update Permission',
    description: 'Update permission information',
    displayOrder: 3,
    isPublic: false,
  },
  {
    code: PERMISSION_PERMISSIONS.DELETE,
    name: 'Delete Permission',
    description: 'Delete a permission',
    displayOrder: 4,
    isPublic: false,
  },
];
