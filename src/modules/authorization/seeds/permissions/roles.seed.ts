import { Permission } from '../../entities/permission.entity';
import { ROLE_PERMISSIONS } from '../../constants';

export const rolePermissions: Partial<Permission>[] = [
  {
    code: ROLE_PERMISSIONS.CREATE,
    name: 'Create Role',
    description: 'Create a new role',
    displayOrder: 1,
    isPublic: false,
  },
  {
    code: ROLE_PERMISSIONS.READ,
    name: 'Read Role',
    description: 'Read role information',
    displayOrder: 2,
    isPublic: false,
  },
  {
    code: ROLE_PERMISSIONS.UPDATE,
    name: 'Update Role',
    description: 'Update role information',
    displayOrder: 3,
    isPublic: false,
  },
  {
    code: ROLE_PERMISSIONS.DELETE,
    name: 'Delete Role',
    description: 'Delete a role',
    displayOrder: 4,
    isPublic: false,
  },
];
