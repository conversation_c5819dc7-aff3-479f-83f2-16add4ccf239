import { PRODUCT_PERMISSIONS } from '../../constants';

// Define categories permissions
const CATEGORY_PERMISSIONS = {
  READ: 'categories.read',
};

// Define brands permissions
const BRAND_PERMISSIONS = {
  READ: 'brands.read',
};

// Define attributes permissions
const ATTRIBUTE_PERMISSIONS = {
  READ: 'attributes.read',
};

/**
 * Permission dependencies for products
 * Format: { permissionCode, dependentPermissionCode }
 * This means that when permissionCode is granted, dependentPermissionCode is also granted
 */
export const productPermissionDependencies = [
  // When creating a product, user needs to read categories, brands, and attributes
  {
    permissionCode: PRODUCT_PERMISSIONS.CREATE,
    dependentPermissionCode: CATEGORY_PERMISSIONS.READ,
  },
  {
    permissionCode: PRODUCT_PERMISSIONS.CREATE,
    dependentPermissionCode: BRAND_PERMISSIONS.READ,
  },
  {
    permissionCode: PRODUCT_PERMISSIONS.CREATE,
    dependentPermissionCode: ATTRIBUTE_PERMISSIONS.READ,
  },
  
  // When updating a product, user needs to read categories, brands, and attributes
  {
    permissionCode: PRODUCT_PERMISSIONS.UPDATE,
    dependentPermissionCode: CATEGORY_PERMISSIONS.READ,
  },
  {
    permissionCode: PRODUCT_PERMISSIONS.UPDATE,
    dependentPermissionCode: BRAND_PERMISSIONS.READ,
  },
  {
    permissionCode: PRODUCT_PERMISSIONS.UPDATE,
    dependentPermissionCode: ATTRIBUTE_PERMISSIONS.READ,
  },
];
