import { Role } from '../entities/role.entity';

export const roles: Partial<Role>[] = [
  {
    name: 'Admin',
    description: 'Administrator with full access',
    isDefault: false,
    isSystem: true,
  },
  {
    name: 'User',
    description: 'Regular user with limited access',
    isDefault: true,
    isSystem: true,
  },
  {
    name: 'Product Manager',
    description: 'User who can manage products',
    isDefault: false,
    isSystem: false,
  },
];
