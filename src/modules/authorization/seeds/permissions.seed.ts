import { Permission } from '../entities/permission.entity';

export const permissions: Partial<Permission>[] = [
  // User permissions
  {
    code: 'users.create',
    name: 'Create User',
    description: 'Create a new user',
    displayOrder: 1,
    isPublic: false,
    groupId: 1, // Users group
  },
  {
    code: 'users.read',
    name: 'Read User',
    description: 'Read user information',
    displayOrder: 2,
    isPublic: false,
    groupId: 1, // Users group
  },
  {
    code: 'users.update',
    name: 'Update User',
    description: 'Update user information',
    displayOrder: 3,
    isPublic: false,
    groupId: 1, // Users group
  },
  {
    code: 'users.delete',
    name: 'Delete User',
    description: 'Delete a user',
    displayOrder: 4,
    isPublic: false,
    groupId: 1, // Users group
  },

  // Role permissions
  {
    code: 'roles.create',
    name: 'Create Role',
    description: 'Create a new role',
    displayOrder: 1,
    isPublic: false,
    groupId: 2, // Roles group
  },
  {
    code: 'roles.read',
    name: 'Read <PERSON>',
    description: 'Read role information',
    displayOrder: 2,
    isPublic: false,
    groupId: 2, // Roles group
  },
  {
    code: 'roles.update',
    name: 'Update Role',
    description: 'Update role information',
    displayOrder: 3,
    isPublic: false,
    groupId: 2, // Roles group
  },
  {
    code: 'roles.delete',
    name: 'Delete Role',
    description: 'Delete a role',
    displayOrder: 4,
    isPublic: false,
    groupId: 2, // Roles group
  },

  // Permission permissions
  {
    code: 'permissions.create',
    name: 'Create Permission',
    description: 'Create a new permission',
    displayOrder: 1,
    isPublic: false,
    groupId: 3, // Permissions group
  },
  {
    code: 'permissions.read',
    name: 'Read Permission',
    description: 'Read permission information',
    displayOrder: 2,
    isPublic: false,
    groupId: 3, // Permissions group
  },
  {
    code: 'permissions.update',
    name: 'Update Permission',
    description: 'Update permission information',
    displayOrder: 3,
    isPublic: false,
    groupId: 3, // Permissions group
  },
  {
    code: 'permissions.delete',
    name: 'Delete Permission',
    description: 'Delete a permission',
    displayOrder: 4,
    isPublic: false,
    groupId: 3, // Permissions group
  },

  // Permission Group permissions
  {
    code: 'permission-groups.create',
    name: 'Create Permission Group',
    description: 'Create a new permission group',
    displayOrder: 1,
    isPublic: false,
    groupId: 4, // Permission Groups group
  },
  {
    code: 'permission-groups.read',
    name: 'Read Permission Group',
    description: 'Read permission group information',
    displayOrder: 2,
    isPublic: false,
    groupId: 4, // Permission Groups group
  },
  {
    code: 'permission-groups.update',
    name: 'Update Permission Group',
    description: 'Update permission group information',
    displayOrder: 3,
    isPublic: false,
    groupId: 4, // Permission Groups group
  },
  {
    code: 'permission-groups.delete',
    name: 'Delete Permission Group',
    description: 'Delete a permission group',
    displayOrder: 4,
    isPublic: false,
    groupId: 4, // Permission Groups group
  },

  // User Role permissions
  {
    code: 'user-roles.assign',
    name: 'Assign Roles to User',
    description: 'Assign roles to a user',
    displayOrder: 5,
    isPublic: false,
    groupId: 1, // Users group
  },
  {
    code: 'user-roles.read',
    name: 'Read User Roles',
    description: 'Read roles assigned to a user',
    displayOrder: 6,
    isPublic: false,
    groupId: 1, // Users group
  },
  {
    code: 'user-roles.delete',
    name: 'Delete User Roles',
    description: 'Remove roles from a user',
    displayOrder: 7,
    isPublic: false,
    groupId: 1, // Users group
  },

  // Product permissions
  {
    code: 'products.create',
    name: 'Create Product',
    description: 'Create a new product',
    displayOrder: 1,
    isPublic: false,
    groupId: 5, // Products group
  },
  {
    code: 'products.read',
    name: 'Read Product',
    description: 'Read product information',
    displayOrder: 2,
    isPublic: true, // Public permission
    groupId: 5, // Products group
  },
  {
    code: 'products.update',
    name: 'Update Product',
    description: 'Update product information',
    displayOrder: 3,
    isPublic: false,
    groupId: 5, // Products group
  },
  {
    code: 'products.delete',
    name: 'Delete Product',
    description: 'Delete a product',
    displayOrder: 4,
    isPublic: false,
    groupId: 5, // Products group
  },
];
