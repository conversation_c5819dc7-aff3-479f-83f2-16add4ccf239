import { RolePermission } from '../entities/role-permission.entity';

export const rolePermissions: Partial<RolePermission>[] = [
  // Admin role has all permissions
  // Users permissions
  { roleId: 1, permissionId: 1 }, // users.create
  { roleId: 1, permissionId: 2 }, // users.read
  { roleId: 1, permissionId: 3 }, // users.update
  { roleId: 1, permissionId: 4 }, // users.delete
  { roleId: 1, permissionId: 19 }, // user-roles.assign
  { roleId: 1, permissionId: 20 }, // user-roles.read
  { roleId: 1, permissionId: 21 }, // user-roles.delete

  // Roles permissions
  { roleId: 1, permissionId: 5 }, // roles.create
  { roleId: 1, permissionId: 6 }, // roles.read
  { roleId: 1, permissionId: 7 }, // roles.update
  { roleId: 1, permissionId: 8 }, // roles.delete

  // Permissions permissions
  { roleId: 1, permissionId: 9 }, // permissions.create
  { roleId: 1, permissionId: 10 }, // permissions.read
  { roleId: 1, permissionId: 11 }, // permissions.update
  { roleId: 1, permissionId: 12 }, // permissions.delete

  // Permission Groups permissions
  { roleId: 1, permissionId: 13 }, // permission-groups.create
  { roleId: 1, permissionId: 14 }, // permission-groups.read
  { roleId: 1, permissionId: 15 }, // permission-groups.update
  { roleId: 1, permissionId: 16 }, // permission-groups.delete

  // Products permissions
  { roleId: 1, permissionId: 22 }, // products.create
  { roleId: 1, permissionId: 23 }, // products.read
  { roleId: 1, permissionId: 24 }, // products.update
  { roleId: 1, permissionId: 25 }, // products.delete

  // User role has limited permissions
  { roleId: 2, permissionId: 23 }, // products.read

  // Product Manager role has product permissions
  { roleId: 3, permissionId: 22 }, // products.create
  { roleId: 3, permissionId: 23 }, // products.read
  { roleId: 3, permissionId: 24 }, // products.update
  { roleId: 3, permissionId: 25 }, // products.delete
];
