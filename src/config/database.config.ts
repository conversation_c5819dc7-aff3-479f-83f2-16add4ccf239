import { registerAs } from '@nestjs/config';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

/**
 * Database configuration
 * This configuration is used by the database module to connect to the database
 * It supports both PostgreSQL and MongoDB, but we're only using PostgreSQL for now
 */
export default registerAs('database', () => ({
  // PostgreSQL configuration
  type: process.env.DATABASE_TYPE || 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432', 10),
  username: process.env.DATABASE_USERNAME || 'postgres',
  password: process.env.DATABASE_PASSWORD || 'postgres',
  database: process.env.DATABASE_NAME || 'onecommerce',
  synchronize: process.env.DATABASE_SYNCHRONIZE === 'true',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../core/database/migrations/*{.ts,.js}'],
  namingStrategy: new SnakeNamingStrategy(),

  // MongoDB configuration (for future use)
  // When switching to MongoDB, you would implement a new repository
  // that uses this configuration
  // mongodb: {
  //   uri: process.env.DATABASE_MONGODB_URI || 'mongodb://localhost:27017',
  //   database: process.env.DATABASE_MONGODB_DATABASE || 'onecommerce',
  // },
}));
