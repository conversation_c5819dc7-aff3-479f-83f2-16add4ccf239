{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/*": ["src/*"], "@common/*": ["src/common/*"], "@config/*": ["src/config/*"], "@core/*": ["src/core/*"], "@modules/*": ["src/modules/*"], "@shared/*": ["src/shared/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}