#!/bin/bash

# Thêm ObjectLiteral vào các file filter
find src/common/database/filters -name "*.ts" -type f -exec sed -i '' 's/import { SelectQueryBuilder } from '\''typeorm'\'';/import { SelectQueryBuilder, ObjectLiteral } from '\''typeorm'\'';/g' {} \;

# Sửa các phương thức apply
find src/common/database/filters -name "*.ts" -type f -exec sed -i '' 's/apply<T>(/apply<T extends ObjectLiteral>(/g' {} \;

# Sửa các phương thức private
find src/common/database/filters -name "*.ts" -type f -exec sed -i '' 's/private applyAnd<T>(/private applyAnd<T extends ObjectLiteral>(/g' {} \;
find src/common/database/filters -name "*.ts" -type f -exec sed -i '' 's/private applyOr<T>(/private applyOr<T extends ObjectLiteral>(/g' {} \;

# Sửa các SelectQueryBuilder
find src/common/database/filters -name "*.ts" -type f -exec sed -i '' 's/new SelectQueryBuilder<T>/new SelectQueryBuilder<T extends ObjectLiteral ? T : never>/g' {} \;
