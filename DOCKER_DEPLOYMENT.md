# Docker Deployment Guide

This guide explains how to deploy the OneCommerce API using Docker for production environments.

## Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed on your server
- Git installed on your server

## Deployment Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd onecommerce-backend
```

### 2. Configure Environment Variables

Create a `.env` file based on the `.env.example` template:

```bash
cp .env.example .env
```

Edit the `.env` file and set appropriate values for your production environment:

```
# Database Configuration
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USERNAME=your_db_username
DATABASE_PASSWORD=your_secure_password
DATABASE_NAME=onecommerce
DATABASE_SYNCHRONIZE=false

# JWT Configuration
JWT_SECRET=your_very_secure_jwt_secret
JWT_EXPIRES_IN=1d

# App Configuration
PORT=3000
NODE_ENV=production
```

### 3. Build and Start the Containers

```bash
docker-compose up -d
```

This command will:
- Build the API container using the Dockerfile
- Start the PostgreSQL database container
- Connect both containers to a Docker network

### 4. Verify Deployment

Check if the containers are running:

```bash
docker-compose ps
```

Access the API at:
```
http://your-server-ip:3000/api/v1
```

Access the Swagger documentation at:
```
http://your-server-ip:3000/api/v1/docs
```

### 5. View Logs

```bash
# View logs from all services
docker-compose logs

# View logs from the API service
docker-compose logs api

# Follow logs in real-time
docker-compose logs -f api
```

## Scaling and Management

### Stopping the Services

```bash
docker-compose down
```

### Restarting the Services

```bash
docker-compose restart
```

### Updating the Application

```bash
# Pull the latest code
git pull

# Rebuild and restart the containers
docker-compose up -d --build
```

## Database Management

### Accessing the Database

```bash
docker exec -it onecommerce-postgres psql -U your_db_username -d onecommerce
```

### Backing Up the Database

```bash
docker exec -t onecommerce-postgres pg_dump -U your_db_username onecommerce > backup_$(date +%Y-%m-%d_%H-%M-%S).sql
```

### Restoring a Database Backup

```bash
cat backup_file.sql | docker exec -i onecommerce-postgres psql -U your_db_username -d onecommerce
```

## Troubleshooting

### Container Won't Start

Check the logs for errors:

```bash
docker-compose logs api
```

### Database Connection Issues

Verify the database environment variables in the `.env` file and ensure they match the PostgreSQL container configuration.

### Performance Tuning

For production environments with higher load, consider adjusting the PostgreSQL configuration and implementing a reverse proxy like Nginx in front of the API.
